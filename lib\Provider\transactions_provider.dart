import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../Repository/transactions_repo.dart';
import '../model/sale_transaction_model.dart';

TransitionRepo _transitionRepo = TransitionRepo(); // متغير غير مستخدم
final _transitionProvider = FutureProvider.autoDispose<List<SaleTransactionModel>>((ref) => transitionRepo.getAllTransition()); // متغير غير مستخدم

PurchaseTransitionRepo _purchaseTransitionRepo = PurchaseTransitionRepo(); // متغير غير مستخدم
final _purchaseTransitionProvider = FutureProvider.autoDispose<List<dynamic>>((ref) => purchaseTransitionRepo.getAllTransition()); // متغير غير مستخدم

QuotationRepo _quotationRepo = QuotationRepo(); // متغير غير مستخدم
final _quotationProvider = FutureProvider.autoDispose<List<SaleTransactionModel>>((ref) => quotationRepo.getAllQuotation()); // متغير غير مستخدم

QuotationHistoryRepo _quotationHistoryRepo = QuotationHistoryRepo(); // متغير غير مستخدم
final _quotationHistoryProvider = FutureProvider.autoDispose<List<SaleTransactionModel>>((ref) => quotationHistoryRepo.getAllQuotationHistory()); // متغير غير مستخدم
