#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لفحص الأخطاء المتبقية في مشروع Flutter
يبحث عن الأخطاء الشائعة التي قد تكون متبقية بعد الإصلاح

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
import re
import sys
from pathlib import Path

class ErrorChecker:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.errors_found = []

    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")

    def check_file_for_errors(self, file_path):
        """فحص ملف واحد للبحث عن أخطاء"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_errors = []
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                # فحص الأخطاء الشائعة
                
                # 1. استخدام FirebaseDatabase بدون استيراد الخدمة
                if 'FirebaseDatabase.instance' in line and 'FirebaseDatabaseService' not in content:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام FirebaseDatabase.instance بدون استيراد FirebaseDatabaseService',
                        'code': line.strip()
                    })
                
                # 2. استخدام jsonDecode/jsonEncode بدون dart:convert
                if ('jsonDecode' in line or 'jsonEncode' in line) and "import 'dart:convert';" not in content:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام jsonDecode/jsonEncode بدون استيراد dart:convert',
                        'code': line.strip()
                    })
                
                # 3. استخدام ImagePickerWeb
                if 'ImagePickerWeb' in line:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام ImagePickerWeb (يجب استبداله بـ ImagePicker)',
                        'code': line.strip()
                    })
                
                # 4. استخدام kIsWeb مع image picker
                if 'if (kIsWeb)' in line and 'ImagePicker' in content:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام kIsWeb مع ImagePicker (غير ضروري لـ Windows)',
                        'code': line.strip()
                    })
                
                # 5. استيرادات غير مستخدمة شائعة
                unused_imports = [
                    'package:firebase_database/firebase_database.dart',
                    'package:image_picker_web/image_picker_web.dart',
                    'package:flutter/foundation.dart'
                ]
                
                for unused_import in unused_imports:
                    if f"import '{unused_import}';" in line:
                        # فحص إذا كان الاستيراد مستخدم فعلاً
                        if unused_import == 'package:firebase_database/firebase_database.dart' and 'FirebaseDatabase' not in content:
                            file_errors.append({
                                'line': line_num,
                                'error': f'استيراد غير مستخدم: {unused_import}',
                                'code': line.strip()
                            })
                        elif unused_import == 'package:image_picker_web/image_picker_web.dart':
                            file_errors.append({
                                'line': line_num,
                                'error': f'استيراد قديم: {unused_import}',
                                'code': line.strip()
                            })
                        elif unused_import == 'package:flutter/foundation.dart' and 'kIsWeb' not in content and 'debugPrint' not in content and 'kDebugMode' not in content:
                            file_errors.append({
                                'line': line_num,
                                'error': f'استيراد غير مستخدم: {unused_import}',
                                'code': line.strip()
                            })
                
                # 6. استخدام العملة الخاطئة
                if "currency: '\\$'" in line:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام عملة خاطئة ($ بدلاً من ج.م)',
                        'code': line.strip()
                    })
                
                # 7. استخدام اللغة الخاطئة
                if "currentLocale: 'en'" in line:
                    file_errors.append({
                        'line': line_num,
                        'error': 'استخدام لغة خاطئة (en بدلاً من ar)',
                        'code': line.strip()
                    })
            
            if file_errors:
                self.errors_found.append({
                    'file': str(file_path),
                    'errors': file_errors
                })
                
        except Exception as e:
            print(f"❌ خطأ في فحص {file_path}: {e}")

    def run(self):
        """تشغيل الفحص"""
        print("🔍 فحص الأخطاء المتبقية في مشروع Flutter...")
        
        self.find_dart_files()
        
        for file_path in self.dart_files:
            self.check_file_for_errors(file_path)
        
        self.print_results()

    def print_results(self):
        """طباعة النتائج"""
        print("\n" + "="*60)
        print("📊 نتائج فحص الأخطاء")
        print("="*60)
        
        if not self.errors_found:
            print("🎉 ممتاز! لم يتم العثور على أخطاء شائعة في المشروع!")
            print("✅ المشروع جاهز للتشغيل")
        else:
            total_errors = sum(len(file_data['errors']) for file_data in self.errors_found)
            print(f"⚠️  تم العثور على {total_errors} خطأ في {len(self.errors_found)} ملف")
            
            for file_data in self.errors_found:
                print(f"\n📁 {file_data['file']}")
                for error in file_data['errors']:
                    print(f"  ❌ السطر {error['line']}: {error['error']}")
                    print(f"     الكود: {error['code']}")
        
        print("\n" + "="*60)

def main():
    checker = ErrorChecker(".")
    checker.run()

if __name__ == "__main__":
    main()
