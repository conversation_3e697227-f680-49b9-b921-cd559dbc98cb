import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

/// خدمة PDF مخصصة لنظام Windows
class WindowsPdfService {
  
  /// طباعة PDF على Windows
  static Future<void> printPdf({
    required Uint8List pdfData,
    String? fileName,
  }) async {
    try {
      if (kIsWeb) {
        // للويب - استخدام printing العادي
        await Printing.layoutPdf(
          dynamicLayout: true,
          onLayout: (PdfPageFormat format) async => pdfData,
        );
      } else if (Platform.isWindows) {
        // لـ Windows - حفظ الملف وفتحه
        await _saveAndOpenPdf(pdfData, fileName ?? 'document');
      } else {
        // للمنصات الأخرى - استخدام printing العادي
        await Printing.layoutPdf(
          dynamicLayout: true,
          onLayout: (PdfPageFormat format) async => pdfData,
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في طباعة PDF: $e');
      EasyLoading.showError('خطأ في طباعة PDF: $e');
    }
  }

  /// حفظ وفتح PDF على Windows
  static Future<void> _saveAndOpenPdf(Uint8List pdfData, String fileName) async {
    try {
      // الحصول على مجلد التحميلات
      final directory = await getDownloadsDirectory() ?? 
                       await getApplicationDocumentsDirectory();
      
      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${directory.path}/${fileName}_$timestamp.pdf');
      
      // كتابة البيانات
      await file.writeAsBytes(pdfData);
      
      // فتح الملف باستخدام التطبيق الافتراضي
      await _openFile(file.path);
      
      EasyLoading.showSuccess('تم حفظ PDF في: ${file.path}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ PDF: $e');
      EasyLoading.showError('خطأ في حفظ PDF: $e');
    }
  }

  /// فتح ملف باستخدام التطبيق الافتراضي
  static Future<void> _openFile(String filePath) async {
    try {
      if (Platform.isWindows) {
        await Process.run('start', ['', filePath], runInShell: true);
      } else if (Platform.isMacOS) {
        await Process.run('open', [filePath]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [filePath]);
      }
    } catch (e) {
      debugPrint('❌ خطأ في فتح الملف: $e');
      // لا نعرض خطأ للمستخدم هنا لأن الملف تم حفظه بنجاح
    }
  }

  /// طباعة مباشرة على Windows (إذا كان متاحاً)
  static Future<void> directPrint(Uint8List pdfData, {BuildContext? context}) async {
    try {
      if (Platform.isWindows && context != null) {
        // محاولة الطباعة المباشرة
        final printer = await Printing.pickPrinter(context: context);
        if (printer != null) {
          await Printing.directPrintPdf(
            printer: printer,
            onLayout: (format) async => pdfData,
          );
        } else {
          await printPdf(pdfData: pdfData);
        }
      } else {
        // للمنصات الأخرى أو عدم وجود context
        await printPdf(pdfData: pdfData);
      }
    } catch (e) {
      debugPrint('❌ خطأ في الطباعة المباشرة: $e');
      // العودة للطريقة العادية
      await printPdf(pdfData: pdfData);
    }
  }

  /// معاينة PDF قبل الطباعة
  static Future<void> previewPdf({
    required Uint8List pdfData,
    String? title,
  }) async {
    try {
      await Printing.layoutPdf(
        dynamicLayout: true,
        name: title ?? 'معاينة المستند',
        onLayout: (PdfPageFormat format) async => pdfData,
      );
    } catch (e) {
      debugPrint('❌ خطأ في معاينة PDF: $e');
      // العودة لحفظ الملف
      await _saveAndOpenPdf(pdfData, title ?? 'document');
    }
  }

  /// مشاركة PDF
  static Future<void> sharePdf({
    required Uint8List pdfData,
    String? fileName,
    String? subject,
  }) async {
    try {
      await Printing.sharePdf(
        bytes: pdfData,
        filename: '${fileName ?? 'document'}.pdf',
        subject: subject,
      );
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة PDF: $e');
      // العودة لحفظ الملف
      await _saveAndOpenPdf(pdfData, fileName ?? 'document');
    }
  }
}
