#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الاستيراد المتبقية في مشروع Flutter

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
import re
from pathlib import Path

class ImportFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.changes_made = 0

    def get_relative_path_to_services(self, file_path):
        """حساب المسار النسبي لمجلد services"""
        try:
            relative_to_lib = file_path.relative_to(self.project_path / 'lib')
            depth = len(relative_to_lib.parts) - 1
            
            if depth == 0:
                return ""
            else:
                return "../" * depth
        except:
            return "../"

    def fix_firebase_service_import(self, file_path):
        """إصلاح استيراد FirebaseDatabaseService"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص إذا كان الملف يستخدم FirebaseDatabaseService
            if 'FirebaseDatabaseService' in content:
                # فحص إذا كان الاستيراد موجود
                if "firebase_database_service.dart" not in content:
                    # حساب المسار النسبي
                    relative_path = self.get_relative_path_to_services(file_path)
                    import_line = f"import '{relative_path}services/firebase_database_service.dart';"
                    
                    # البحث عن مكان إدراج الاستيراد
                    lines = content.split('\n')
                    new_lines = []
                    import_added = False
                    
                    for i, line in enumerate(lines):
                        new_lines.append(line)
                        
                        # إضافة الاستيراد بعد آخر استيراد
                        if line.startswith("import ") and not import_added:
                            # البحث عن نهاية قسم الاستيرادات
                            next_line_index = i + 1
                            while next_line_index < len(lines) and (lines[next_line_index].startswith("import ") or lines[next_line_index].strip() == ""):
                                next_line_index += 1
                            
                            if next_line_index == i + 1 or not lines[i + 1].startswith("import "):
                                new_lines.append(import_line)
                                import_added = True
                    
                    if not import_added:
                        # إضافة في البداية إذا لم يتم العثور على مكان مناسب
                        new_lines.insert(0, import_line)
                    
                    content = '\n'.join(new_lines)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ تم إضافة استيراد FirebaseDatabaseService إلى {file_path.name}")
                    self.changes_made += 1
                    return True
        
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")
        
        return False

    def fix_uint8list_import(self, file_path):
        """إصلاح استيراد Uint8List"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص إذا كان الملف يستخدم Uint8List
            if 'Uint8List' in content:
                # فحص إذا كان الاستيراد موجود
                if "import 'dart:typed_data';" not in content and "import 'package:flutter/services.dart';" not in content:
                    # إضافة استيراد dart:typed_data
                    lines = content.split('\n')
                    new_lines = []
                    import_added = False
                    
                    for i, line in enumerate(lines):
                        if line.startswith("import 'dart:") and not import_added:
                            new_lines.append(line)
                            new_lines.append("import 'dart:typed_data';")
                            import_added = True
                        elif line.startswith("import ") and not import_added and not line.startswith("import 'dart:"):
                            new_lines.append("import 'dart:typed_data';")
                            new_lines.append(line)
                            import_added = True
                        else:
                            new_lines.append(line)
                    
                    if not import_added:
                        new_lines.insert(0, "import 'dart:typed_data';")
                    
                    content = '\n'.join(new_lines)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ تم إضافة استيراد Uint8List إلى {file_path.name}")
                    self.changes_made += 1
                    return True
        
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")
        
        return False

    def fix_all_files(self):
        """إصلاح جميع الملفات"""
        print("🔧 إصلاح مشاكل الاستيراد المتبقية...")
        
        # البحث عن جميع ملفات Dart
        dart_files = []
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            dart_files.append(file_path)
        
        print(f"🔍 فحص {len(dart_files)} ملف...")
        
        for file_path in dart_files:
            self.fix_firebase_service_import(file_path)
            self.fix_uint8list_import(file_path)
        
        print(f"\n✅ تم إجراء {self.changes_made} إصلاح")

def main():
    fixer = ImportFixer(".")
    fixer.fix_all_files()

if __name__ == "__main__":
    main()
