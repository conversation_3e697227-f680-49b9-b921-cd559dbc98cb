import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/sale_transaction_model.dart';

class SalesReturnRepo {
  Future<List<SaleTransactionModel>> getAllTransition() async {
    List<SaleTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Sales Return').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(SaleTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات إرجاع المبيعات: $e');
    }
    return transitionList;
  }
}
