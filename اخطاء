[{
	"resource": "/D:/work/pos-win/lib/debug/login_debug.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "unused_local_variable",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/unused_local_variable",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 4,
	"message": "The value of the local variable 'idToken' isn't used.\nTry removing the variable or using it.",
	"source": "dart",
	"startLineNumber": 44,
	"startColumn": 17,
	"endLineNumber": 44,
	"endColumn": 24
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_class",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_class",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined class 'DatabaseReference'.\nTry changing the name to the name of an existing class, or creating a class with the name 'DatabaseReference'.",
	"source": "dart",
	"startLineNumber": 381,
	"startColumn": 33,
	"endLineNumber": 381,
	"endColumn": 50
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 381,
	"startColumn": 57,
	"endLineNumber": 381,
	"endColumn": 80
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 442,
	"startColumn": 17,
	"endLineNumber": 442,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 445,
	"startColumn": 11,
	"endLineNumber": 445,
	"endColumn": 34
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 461,
	"startColumn": 17,
	"endLineNumber": 461,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 474,
	"startColumn": 17,
	"endLineNumber": 474,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 24,
	"startColumn": 9,
	"endLineNumber": 24,
	"endColumn": 36
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_class",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_class",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined class 'DatabaseReference'.\nTry changing the name to the name of an existing class, or creating a class with the name 'DatabaseReference'.",
	"source": "dart",
	"startLineNumber": 384,
	"startColumn": 35,
	"endLineNumber": 384,
	"endColumn": 52
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 384,
	"startColumn": 59,
	"endLineNumber": 384,
	"endColumn": 82
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 451,
	"startColumn": 17,
	"endLineNumber": 451,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 454,
	"startColumn": 11,
	"endLineNumber": 454,
	"endColumn": 34
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'FirebaseDatabaseService'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 470,
	"startColumn": 17,
	"endLineNumber": 470,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pop UP/Pos Sale/tab_show_payment_popup.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 24,
	"startColumn": 9,
	"endLineNumber": 24,
	"endColumn": 28
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "uri_does_not_exist",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/uri_does_not_exist",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Target of URI doesn't exist: 'package:printing/printing.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
	"source": "dart",
	"startLineNumber": 7,
	"startColumn": 8,
	"endLineNumber": 7,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'PdfGoogleFonts'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 92,
	"startColumn": 17,
	"endLineNumber": 92,
	"endColumn": 31
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'PdfGoogleFonts'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 93,
	"startColumn": 17,
	"endLineNumber": 93,
	"endColumn": 31
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'PdfGoogleFonts'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 94,
	"startColumn": 17,
	"endLineNumber": 94,
	"endColumn": 31
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "depend_on_referenced_packages",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/depend_on_referenced_packages",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "The imported package 'printing' isn't a dependency of the importing package.\nTry adding a dependency for 'printing' in the 'pubspec.yaml' file.",
	"source": "dart",
	"startLineNumber": 7,
	"startColumn": 8,
	"endLineNumber": 7,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "unnecessary_string_escapes",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/unnecessary_string_escapes",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Unnecessary escape in string literal.\nRemove the '\\' escape.",
	"source": "dart",
	"startLineNumber": 35,
	"startColumn": 35,
	"endLineNumber": 35,
	"endColumn": 36
},{
	"resource": "/D:/work/pos-win/lib/Screen/Widgets/Pdf/invoice_pdf.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'Table.fromTextArray' is deprecated and shouldn't be used. Use TableHelper.fromTextArray() instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 424,
	"startColumn": 12,
	"endLineNumber": 424,
	"endColumn": 34,
	"tags": [
		2
	]
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/tablet_due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'MdiIcons'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 57,
	"startColumn": 19,
	"endLineNumber": 57,
	"endColumn": 27
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/tablet_due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dart:html' is deprecated and shouldn't be used. Use package:web and dart:js_interop instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 20,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/tablet_due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "avoid_web_libraries_in_flutter",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/avoid_web_libraries_in_flutter",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Don't use web-only libraries outside Flutter web plugins.\nTry finding a different library for your needs.",
	"source": "dart",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 20
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/tablet_due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 15,
	"startColumn": 9,
	"endLineNumber": 15,
	"endColumn": 22
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/tablet_due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 186,
	"startColumn": 50,
	"endLineNumber": 186,
	"endColumn": 71,
	"tags": [
		2
	]
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/sale_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'MdiIcons'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 73,
	"startColumn": 21,
	"endLineNumber": 73,
	"endColumn": 29
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/sale_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dart:html' is deprecated and shouldn't be used. Use package:web and dart:js_interop instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 2,
	"startColumn": 1,
	"endLineNumber": 2,
	"endColumn": 20,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/sale_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 18,
	"startColumn": 9,
	"endLineNumber": 18,
	"endColumn": 20
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/sale_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 218,
	"startColumn": 46,
	"endLineNumber": 218,
	"endColumn": 67,
	"tags": [
		2
	]
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/purchase_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'MdiIcons'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 79,
	"startColumn": 21,
	"endLineNumber": 79,
	"endColumn": 29
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/purchase_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dart:html' is deprecated and shouldn't be used. Use package:web and dart:js_interop instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 2,
	"startColumn": 1,
	"endLineNumber": 2,
	"endColumn": 20,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/purchase_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 20,
	"startColumn": 9,
	"endLineNumber": 20,
	"endColumn": 24
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/purchase_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 208,
	"startColumn": 46,
	"endLineNumber": 208,
	"endColumn": 67,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/purchase_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dataRowHeight' is deprecated and shouldn't be used. Migrate to use dataRowMinHeight and dataRowMaxHeight instead. This feature was deprecated after v3.7.0-5.0.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 214,
	"startColumn": 29,
	"endLineNumber": 214,
	"endColumn": 42,
	"tags": [
		2
	]
}]
[{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_identifier",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_identifier",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Undefined name 'MdiIcons'.\nTry correcting the name to one that is defined, or defining the name.",
	"source": "dart",
	"startLineNumber": 60,
	"startColumn": 19,
	"endLineNumber": 60,
	"endColumn": 27
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dart:html' is deprecated and shouldn't be used. Use package:web and dart:js_interop instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 2,
	"startColumn": 1,
	"endLineNumber": 2,
	"endColumn": 20,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "use_super_parameters",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/use_super_parameters",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "Parameter 'key' could be a super parameter.\nTrying converting 'key' to a super parameter.",
	"source": "dart",
	"startLineNumber": 17,
	"startColumn": 9,
	"endLineNumber": 17,
	"endColumn": 19
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'MaterialStateProperty' is deprecated and shouldn't be used. Use WidgetStateProperty instead. Moved to the Widgets layer to make code available outside of Material. This feature was deprecated after v3.19.0-0.3.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 188,
	"startColumn": 50,
	"endLineNumber": 188,
	"endColumn": 71,
	"tags": [
		2
	]
},{
	"resource": "/D:/work/pos-win/lib/Screen/Invoice/due_invoice.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'dataRowHeight' is deprecated and shouldn't be used. Migrate to use dataRowMinHeight and dataRowMaxHeight instead. This feature was deprecated after v3.7.0-5.0.pre.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 194,
	"startColumn": 33,
	"endLineNumber": 194,
	"endColumn": 46,
	"tags": [
		2
	]
}]
[{
	"resource": "/D:/work/pos-win/lib/PDF/pdf 2.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "uri_does_not_exist",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/uri_does_not_exist",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "Target of URI doesn't exist: 'package:printing/printing.dart'.\nTry creating the file referenced by the URI, or try using a URI for a file that does exist.",
	"source": "dart",
	"startLineNumber": 9,
	"startColumn": 8,
	"endLineNumber": 9,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/PDF/pdf 2.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "undefined_method",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/undefined_method",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 8,
	"message": "The method 'networkImage' isn't defined for the type 'GeneratePdf'.\nTry correcting the name to the name of an existing method, or defining a method named 'networkImage'.",
	"source": "dart",
	"startLineNumber": 18,
	"startColumn": 28,
	"endLineNumber": 18,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/PDF/pdf 2.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "unused_import",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/unused_import",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 4,
	"message": "Unused import: '../../model/sale_transaction_model.dart'.\nTry removing the import directive.",
	"source": "dart",
	"startLineNumber": 12,
	"startColumn": 8,
	"endLineNumber": 12,
	"endColumn": 49
},{
	"resource": "/D:/work/pos-win/lib/PDF/pdf 2.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "depend_on_referenced_packages",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/depend_on_referenced_packages",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "The imported package 'printing' isn't a dependency of the importing package.\nTry adding a dependency for 'printing' in the 'pubspec.yaml' file.",
	"source": "dart",
	"startLineNumber": 9,
	"startColumn": 8,
	"endLineNumber": 9,
	"endColumn": 40
},{
	"resource": "/D:/work/pos-win/lib/PDF/pdf 2.dart",
	"owner": "_generated_diagnostic_collection_name_#1",
	"code": {
		"value": "deprecated_member_use",
		"target": {
			"$mid": 1,
			"path": "/diagnostics/deprecated_member_use",
			"scheme": "https",
			"authority": "dart.dev"
		}
	},
	"severity": 2,
	"message": "'Table.fromTextArray' is deprecated and shouldn't be used. Use TableHelper.fromTextArray() instead.\nTry replacing the use of the deprecated member with the replacement.",
	"source": "dart",
	"startLineNumber": 256,
	"startColumn": 19,
	"endLineNumber": 256,
	"endColumn": 41,
	"tags": [
		2
	]
}]