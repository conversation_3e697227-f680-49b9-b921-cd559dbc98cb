تقرير إصلاح مشكلة withOpacity في Flutter
==================================================

التاريخ: 2025-07-16 17:52:46
الملفات المعالجة: 248
إجمالي التغييرات: 296

تفاصيل التغييرات:
------------------------------

الملف: add_profile.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: forgot_password.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: log_in.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: profile_setup.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: sign_up.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: tablet_log_in.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: tablet_profile_set_up.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: tablet_signup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: verify_otp.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: customer_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: due_list_screen.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: add_category.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: edit_category.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: expenses_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: expense_category.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: expense_details.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: expense_edit.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: new_expense.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: total_count_widget.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kRedTextColor.withOpacity(0.2) → Color.fromRGBO(254, 37, 37, 0.2)
  kGreenTextColor.withOpacity(0.2) → Color.fromRGBO(132, 36, 255, 0.2)
  Colors.white.withOpacity(0.5) → Color.fromRGBO(255, 255, 255, 0.5)

الملف: add_income_category.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: edit_income_category.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: income_category.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: income_details.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: income_Edit.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: income_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: new_income.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: inventory_sales.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: ledger_screen.dart
عدد التغييرات: 8
أمثلة على التغييرات:
  Color(0xFF2DB0F6).withOpacity(0.5) → Color.fromRGBO(45, 176, 246, 0.5)
  Color(0xFF15CD75).withOpacity(0.5) → Color.fromRGBO(21, 205, 117, 0.5)
  Color(0xFF15CD75).withOpacity(0.5) → Color.fromRGBO(21, 205, 117, 0.5)

الملف: lossProfit_screen.dart
عدد التغييرات: 9
أمثلة على التغييرات:
  Color(0xFF2DB0F6).withOpacity(0.5) → Color.fromRGBO(45, 176, 246, 0.5)
  Color(0xFF15CD75).withOpacity(0.5) → Color.fromRGBO(21, 205, 117, 0.5)
  Color(0xFFFF2525).withOpacity(.5) → Color.fromRGBO(255, 37, 37, 0.5)

الملف: payment_cancel.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: payment_success.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: pos_sale.dart
عدد التغييرات: 8
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: tablet_pos_sale.dart
عدد التغييرات: 6
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: add_product.dart
عدد التغييرات: 6
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: product.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase.dart
عدد التغييرات: 7
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: purchase_edit.dart
عدد التغييرات: 7
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: purchase_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_returns_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_return_screen.dart
عدد التغييرات: 5
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: quotation_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: current_stock_widget.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: daily_transaction.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: due_reports_wedget.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: ledger_details_widget.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  Color(0xFF2DB0F6).withOpacity(0.5) → Color.fromRGBO(45, 176, 246, 0.5)
  Color(0xFF15CD75).withOpacity(0.5) → Color.fromRGBO(21, 205, 117, 0.5)

الملف: loss_profit_report.dart
عدد التغييرات: 9
أمثلة على التغييرات:
  Color(0xFF2DB0F6).withOpacity(0.5) → Color.fromRGBO(45, 176, 246, 0.5)
  Color(0xFF15CD75).withOpacity(0.5) → Color.fromRGBO(21, 205, 117, 0.5)
  Color(0xFFFF2525).withOpacity(.5) → Color.fromRGBO(255, 37, 37, 0.5)

الملف: purchase_report_widget.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_return_widget.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: quotation_reports_wedget.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: report_screen.dart
عدد التغييرات: 7
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: seles_return_widget.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: sale_edit.dart
عدد التغييرات: 7
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: sale_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: sales_returns_list.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: sales_return_screen.dart
عدد التغييرات: 5
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)
  kGreyTextColor.withOpacity(0.3) → Color.fromRGBO(88, 88, 101, 0.3)

الملف: settings_screen.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: payment.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: purchase_plan.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: subscription_plan_page.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kMainColor.withOpacity(0.2) → Color.fromRGBO(132, 36, 255, 0.2)

الملف: supplier_list.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: create_single_tax.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: edit_group_tax_popUp.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: new_tax_group_popup.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: tax_rates_widget.dart
عدد التغييرات: 12
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  Colors.white.withOpacity(0.1) → Color.fromRGBO(255, 255, 255, 0.1)

الملف: user_role_screen.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: edit_warehouse.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: warehouse_details.dart
عدد التغييرات: 4
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: ware_house_list.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: warranty_screen.dart
عدد التغييرات: 7
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: table_widget.dart
عدد التغييرات: 16
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: export_button.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)

الملف: top_bar_widget.dart
عدد التغييرات: 9
أمثلة على التغييرات:
  Color(0xFF8424FF).withOpacity(0.5) → Color.fromRGBO(132, 36, 255, 0.5)
  Color(0xFF8424FF).withOpacity(0.1) → Color.fromRGBO(132, 36, 255, 0.1)
  Color(0xFFFF2525).withOpacity(0.1) → Color.fromRGBO(255, 37, 37, 0.1)

الملف: add_item_popup.dart
عدد التغييرات: 6
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: due_sale_popup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: show_add_item_popup.dart
عدد التغييرات: 6
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_due_sale_popup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_sale_list_popup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: purchase_show_add_item_popup.dart
عدد التغييرات: 6
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: tab_purchase_due_sale_popup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: tab_purchase_sale_list_popup.dart
عدد التغييرات: 2
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: barcode_generate.dart
عدد التغييرات: 1
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: designation_list.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: employee_list.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)

الملف: salaries_list_screen.dart
عدد التغييرات: 3
أمثلة على التغييرات:
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.1) → Color.fromRGBO(88, 88, 101, 0.1)
  kGreyTextColor.withOpacity(0.2) → Color.fromRGBO(88, 88, 101, 0.2)
