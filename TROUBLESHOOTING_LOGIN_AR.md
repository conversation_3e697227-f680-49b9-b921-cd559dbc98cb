# دليل استكشاف أخطاء تسجيل الدخول وإصلاحها

## المشاكل الشائعة وحلولها

### 1. مشكلة: لا يتم تسجيل الدخول

#### الأسباب المحتملة:
- **خطأ في بيانات الاعتماد**: تأكد من صحة الإيميل وكلمة المرور
- **مشكلة في اتصال Firebase**: تحقق من اتصال الإنترنت
- **خطأ في إعدادات Firebase**: تأكد من صحة إعدادات Firebase

#### الحلول:
1. **تحقق من بيانات الاعتماد**:
   - تأكد من أن الإيميل بالتنسيق الصحيح: `<EMAIL>`
   - تأكد من صحة كلمة المرور
   - تحقق من أن المستخدم موجود في Firebase Auth Console

2. **تحقق من اتصال Firebase**:
   ```bash
   # تشغيل التطبيق في وضع Debug لرؤية رسائل التشخيص
   flutter run -d windows --debug
   ```

3. **فحص إعدادات Firebase**:
   - تأكد من صحة ملف `firebase_options.dart`
   - تحقق من أن المشروع مفعل في Firebase Console

### 2. مشكلة: خطأ "MissingPluginException"

#### السبب:
- عدم دعم `firebase_database` لنظام Windows

#### الحل:
- تم حل هذه المشكلة باستخدام Firebase REST API
- تأكد من أن الملف `lib/services/firebase_database_service.dart` موجود

### 3. مشكلة: خطأ في التحقق من المستخدمين الفرعيين

#### الأسباب المحتملة:
- عدم وجود بيانات في `Admin Panel/User Role`
- خطأ في قراءة البيانات من Firebase Database

#### الحلول:
1. **تحقق من وجود البيانات**:
   - افتح Firebase Console
   - انتقل إلى Realtime Database
   - تحقق من وجود مسار `Admin Panel/User Role`

2. **تحقق من قواعد الأمان**:
   ```json
   {
     "rules": {
       ".read": "auth != null",
       ".write": "auth != null"
     }
   }
   ```

### 4. مشكلة: التطبيق يتوقف عند تسجيل الدخول

#### الأسباب المحتملة:
- خطأ في كود التنقل
- مشكلة في تحميل البيانات

#### الحلول:
1. **فحص سجلات الأخطاء**:
   ```bash
   flutter logs
   ```

2. **تشغيل في وضع Debug**:
   ```bash
   flutter run -d windows --debug
   ```

## خطوات التشخيص

### 1. فحص أساسي
```bash
# تنظيف المشروع
flutter clean

# تحديث التبعيات
flutter pub get

# بناء التطبيق
flutter build windows --release
```

### 2. فحص Firebase
1. افتح Firebase Console
2. تحقق من Authentication > Users
3. تحقق من Realtime Database > Data
4. تحقق من Project Settings > General

### 3. فحص الشبكة
- تأكد من اتصال الإنترنت
- تحقق من عدم حجب Firebase بواسطة Firewall

## رسائل الخطأ الشائعة

### "user-not-found"
- **المعنى**: المستخدم غير موجود في Firebase Auth
- **الحل**: إنشاء حساب جديد أو التأكد من الإيميل

### "wrong-password"
- **المعنى**: كلمة المرور خاطئة
- **الحل**: التأكد من كلمة المرور أو إعادة تعيينها

### "invalid-email"
- **المعنى**: تنسيق الإيميل خاطئ
- **الحل**: التأكد من تنسيق الإيميل

### "user-disabled"
- **المعنى**: تم تعطيل المستخدم
- **الحل**: تفعيل المستخدم من Firebase Console

### "too-many-requests"
- **المعنى**: محاولات كثيرة جداً
- **الحل**: الانتظار قليلاً ثم المحاولة مرة أخرى

## معلومات إضافية

### تنسيق الإيميل
- يتم إضافة `@amrdev.com` تلقائياً
- المستخدم يدخل فقط اسم المستخدم (مثل: `amr`)
- النظام يحوله إلى `<EMAIL>`

### أنواع المستخدمين
1. **مستخدم رئيسي**: له صلاحيات كاملة
2. **مستخدم فرعي**: له صلاحيات محدودة حسب الإعدادات

### ملفات مهمة
- `lib/Repository/login_repo.dart`: منطق تسجيل الدخول
- `lib/services/firebase_database_service.dart`: خدمة قاعدة البيانات
- `lib/firebase_options.dart`: إعدادات Firebase
- `lib/debug/login_debug.dart`: أداة التشخيص

## الحصول على المساعدة

إذا استمرت المشكلة:
1. تحقق من سجلات الأخطاء
2. تشغيل أداة التشخيص
3. التأكد من إعدادات Firebase
4. فحص اتصال الإنترنت
