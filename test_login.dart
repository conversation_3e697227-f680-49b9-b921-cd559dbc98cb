//import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'lib/firebase_options.dart';

// /// اختبار تسجيل الدخول على Windows
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   try {
//     // تهيئة Firebase
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );
//     debugPrint('✅ تم تهيئة Firebase بنجاح');
    
//     // اختبار تسجيل الدخول
//     await testLogin();
    
//   } catch (e) {
//     debugPrint('❌ خطأ في الاختبار: $e');
//   }
// }

// /// اختبار تسجيل الدخول
// Future<void> testLogin() async {
//   debugPrint('\n🔐 اختبار تسجيل الدخول...');
  
//   // بيانات اختبار (استخدم بيانات صحيحة)
//   const testEmail = '<EMAIL>';
//   const testPassword = 'your_password_here'; // ضع كلمة المرور الصحيحة
  
//   try {
//     debugPrint('🔄 محاولة تسجيل الدخول بـ: $testEmail');
    
//     final userCredential = await FirebaseAuth.instance
//         .signInWithEmailAndPassword(
//           email: testEmail, 
//           password: testPassword
//         );
    
//     if (userCredential.user != null) {
//       debugPrint('✅ تم تسجيل الدخول بنجاح!');
//       debugPrint('📧 الإيميل: ${userCredential.user!.email}');
//       debugPrint('🆔 UID: ${userCredential.user!.uid}');
//       debugPrint('✅ تم التحقق: ${userCredential.user!.emailVerified}');
      
//       // اختبار الحصول على ID Token
//       final idToken = await userCredential.user!.getIdToken();
//       debugPrint('🔑 تم الحصول على ID Token بنجاح');
      
//       // تسجيل الخروج
//       await FirebaseAuth.instance.signOut();
//       debugPrint('🚪 تم تسجيل الخروج بنجاح');
      
//     } else {
//       debugPrint('❌ فشل في تسجيل الدخول - لا يوجد مستخدم');
//     }
    
//   } on FirebaseAuthException catch (e) {
//     debugPrint('❌ خطأ في Firebase Auth: ${e.code}');
//     debugPrint('📝 الرسالة: ${e.message}');
    
//     switch (e.code) {
//       case 'user-not-found':
//         debugPrint('💡 الحل: تأكد من وجود المستخدم في Firebase Auth');
//         break;
//       case 'wrong-password':
//         debugPrint('💡 الحل: تأكد من صحة كلمة المرور');
//         break;
//       case 'invalid-email':
//         debugPrint('💡 الحل: تأكد من صحة تنسيق الإيميل');
//         break;
//       case 'user-disabled':
//         debugPrint('💡 الحل: تم تعطيل هذا المستخدم');
//         break;
//       default:
//         debugPrint('💡 خطأ غير معروف: ${e.code}');
//     }
//   } catch (e) {
//     debugPrint('❌ خطأ عام: $e');
//   }
// }
