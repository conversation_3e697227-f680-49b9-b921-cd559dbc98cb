import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';

import '../model/seller_info_model.dart';

class SellerInfoRepo {
  Future<List<SellerInfoModel>> getAllSeller() async {
    List<SellerInfoModel> sellerList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref('Admin Panel').child('Seller List').orderByKey().get();
      for (var element in snapshot.children) {
        var data = SellerInfoModel.fromJson(jsonDecode(jsonEncode(element.value)));
        sellerList.add(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات البائعين: $e');
    }
    return sellerList;
  }
}
