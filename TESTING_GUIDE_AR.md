# دليل اختبار التطبيق على Windows

## خطوات الاختبار الأساسية

### 1. تشغيل التطبيق

```bash
# بناء التطبيق
flutter build windows --release

# تشغيل التطبيق
.\build\windows\x64\runner\Release\MOBIPOS_admin.exe
```

### 2. اختبار تسجيل الدخول

#### بيانات الاختبار:
- **اسم المستخدم**: `amr` (سيتم تحويله إلى `<EMAIL>`)
- **كلمة المرور**: [استخدم كلمة المرور الصحيحة]

#### خطوات الاختبار:
1. افتح التطبيق
2. أدخل اسم المستخدم (بدون @amrdev.com)
3. أدخل كلمة المرور
4. اضغط على "تسجيل الدخول"
5. تحقق من نجاح تسجيل الدخول

#### النتائج المتوقعة:
- ✅ تسجيل دخول ناجح
- ✅ الانتقال إلى الشاشة الرئيسية
- ✅ عرض بيانات المستخدم

### 3. اختبار الوظائف الأساسية

#### أ. إدارة المنتجات
1. انتقل إلى قسم "المنتجات"
2. جرب إضافة منتج جديد
3. جرب تعديل منتج موجود
4. جرب حذف منتج

#### ب. إدارة العملاء
1. انتقل إلى قسم "العملاء"
2. جرب إضافة عميل جديد
3. جرب تعديل بيانات عميل
4. جرب البحث عن عميل

#### ج. المبيعات
1. انتقل إلى قسم "المبيعات"
2. جرب إنشاء فاتورة جديدة
3. جرب إضافة منتجات للفاتورة
4. جرب حفظ الفاتورة

#### د. التقارير
1. انتقل إلى قسم "التقارير"
2. جرب عرض تقرير المبيعات
3. جرب تصدير التقرير
4. جرب طباعة التقرير

### 4. اختبار الاتصال بقاعدة البيانات

#### فحص Firebase Database:
```bash
# تشغيل في وضع Debug لرؤية رسائل التشخيص
flutter run -d windows --debug
```

#### علامات نجاح الاتصال:
- ✅ رسائل "تم الحصول على البيانات بنجاح"
- ✅ عدم ظهور رسائل "MissingPluginException"
- ✅ تحديث البيانات في الوقت الفعلي

### 5. اختبار الأداء

#### مؤشرات الأداء:
- **وقت بدء التطبيق**: يجب أن يكون أقل من 10 ثوانٍ
- **وقت تسجيل الدخول**: يجب أن يكون أقل من 5 ثوانٍ
- **وقت تحميل البيانات**: يجب أن يكون أقل من 3 ثوانٍ
- **استهلاك الذاكرة**: يجب أن يكون معقولاً

#### أدوات المراقبة:
- Task Manager لمراقبة استهلاك الذاكرة
- Flutter DevTools لمراقبة الأداء

## مشاكل محتملة وحلولها

### 1. التطبيق لا يبدأ
**الأسباب المحتملة:**
- ملفات مفقودة
- مشكلة في التبعيات

**الحلول:**
```bash
flutter clean
flutter pub get
flutter build windows --release
```

### 2. خطأ في تسجيل الدخول
**الأسباب المحتملة:**
- بيانات اعتماد خاطئة
- مشكلة في Firebase

**الحلول:**
- تحقق من بيانات الاعتماد
- راجع `TROUBLESHOOTING_LOGIN_AR.md`

### 3. بطء في التطبيق
**الأسباب المحتملة:**
- اتصال إنترنت بطيء
- مشكلة في قاعدة البيانات

**الحلول:**
- تحقق من سرعة الإنترنت
- راقب استهلاك الذاكرة

## قائمة فحص الاختبار

### ✅ الوظائف الأساسية
- [ ] تسجيل الدخول
- [ ] تسجيل الخروج
- [ ] إدارة المنتجات
- [ ] إدارة العملاء
- [ ] المبيعات
- [ ] التقارير

### ✅ الاتصال بقاعدة البيانات
- [ ] قراءة البيانات
- [ ] كتابة البيانات
- [ ] تحديث البيانات
- [ ] حذف البيانات

### ✅ واجهة المستخدم
- [ ] عرض صحيح للنصوص العربية
- [ ] تجاوب مع أحجام الشاشة المختلفة
- [ ] عمل الأزرار والقوائم
- [ ] عرض الرسائل والتنبيهات

### ✅ الأداء
- [ ] سرعة بدء التطبيق
- [ ] سرعة تحميل البيانات
- [ ] استقرار التطبيق
- [ ] استهلاك معقول للذاكرة

## تقرير الاختبار

### معلومات النظام:
- **نظام التشغيل**: Windows [الإصدار]
- **إصدار Flutter**: [الإصدار]
- **تاريخ الاختبار**: [التاريخ]

### نتائج الاختبار:
- **تسجيل الدخول**: ✅ نجح / ❌ فشل
- **إدارة البيانات**: ✅ نجح / ❌ فشل
- **الأداء**: ✅ مقبول / ❌ غير مقبول
- **الاستقرار**: ✅ مستقر / ❌ غير مستقر

### ملاحظات:
[أضف أي ملاحظات أو مشاكل واجهتها]

### توصيات:
[أضف أي توصيات للتحسين]
