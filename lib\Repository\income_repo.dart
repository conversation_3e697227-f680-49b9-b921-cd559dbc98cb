import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';
import 'package:amrdev_win_pos/model/income_modle.dart';

import '../const.dart';

class IncomeRepo {
  Future<List<IncomeModel>> getAllIncome() async {
    List<IncomeModel> allIncome = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Income').orderByKey().get();
      for (var element in snapshot.children) {
        var data = IncomeModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allIncome.add(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات الإيرادات: $e');
    }
    return allIncome;
  }
}
