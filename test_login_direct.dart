import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'lib/firebase_options.dart';
// import 'lib/services/firebase_database_service.dart';

// /// اختبار مباشر لتسجيل الدخول
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   try {
//     // تهيئة Firebase
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );
//     debugPrint('✅ تم تهيئة Firebase بنجاح');
    
//     // اختبار تسجيل الدخول المباشر
//     await testDirectLogin();
    
//   } catch (e) {
//     debugPrint('❌ خطأ في الاختبار: $e');
//   }
// }

// /// اختبار تسجيل الدخول المباشر
// Future<void> testDirectLogin() async {
//   debugPrint('\n🔐 اختبار تسجيل الدخول المباشر...');
  
//   const testEmail = '<EMAIL>';
//   const testPassword = 'your_password_here'; // ضع كلمة المرور الصحيحة
  
//   try {
//     debugPrint('🔄 محاولة تسجيل الدخول...');
    
//     // تسجيل الدخول
//     final userCredential = await FirebaseAuth.instance
//         .signInWithEmailAndPassword(email: testEmail, password: testPassword);
    
//     if (userCredential.user != null) {
//       debugPrint('✅ تم تسجيل الدخول بنجاح!');
//       debugPrint('📧 الإيميل: ${userCredential.user!.email}');
//       debugPrint('🆔 UID: ${userCredential.user!.uid}');
      
//       // اختبار فحص المستخدمين الفرعيين
//       bool isSubUser = await checkSubUser(testEmail);
//       debugPrint('🔍 نتيجة فحص المستخدم الفرعي: $isSubUser');
      
//       // اختبار فحص إعداد الملف الشخصي
//       bool isProfileSetup = await checkProfileSetup(userCredential.user!.uid);
//       debugPrint('🔍 حالة إعداد الملف الشخصي: $isProfileSetup');
      
//       // تحديد المسار التالي
//       if (isSubUser) {
//         debugPrint('👥 المستخدم فرعي - يجب الانتقال للشاشة الرئيسية');
//       } else if (isProfileSetup) {
//         debugPrint('✅ الملف الشخصي معد - يجب الانتقال للشاشة الرئيسية');
//       } else {
//         debugPrint('⚠️ الملف الشخصي غير معد - يجب الانتقال لإعداد الملف الشخصي');
//       }
      
//       // تسجيل الخروج
//       await FirebaseAuth.instance.signOut();
//       debugPrint('🚪 تم تسجيل الخروج بنجاح');
      
//     } else {
//       debugPrint('❌ فشل في تسجيل الدخول - لا يوجد مستخدم');
//     }
    
//   } on FirebaseAuthException catch (e) {
//     debugPrint('❌ خطأ في Firebase Auth: ${e.code}');
//     debugPrint('📝 الرسالة: ${e.message}');
//   } catch (e) {
//     debugPrint('❌ خطأ عام: $e');
//   }
// }

// /// فحص المستخدمين الفرعيين
// Future<bool> checkSubUser(String email) async {
//   try {
//     final snapshot = await FirebaseDatabaseService.ref('Admin Panel')
//         .child('User Role')
//         .orderByKey()
//         .get();
        
//     if (!snapshot.exists || snapshot.value == null) {
//       debugPrint('ℹ️ لا توجد بيانات مستخدمين فرعيين');
//       return false;
//     }
    
//     for (var element in snapshot.children) {
//       try {
//         if (element.value == null) continue;
        
//         final data = element.value as Map;
//         final userEmail = data['email'] ?? '';
        
//         if (userEmail == email) {
//           debugPrint('✅ تم العثور على المستخدم الفرعي: $email');
//           return true;
//         }
//       } catch (e) {
//         debugPrint('❌ خطأ في معالجة بيانات مستخدم فرعي: $e');
//         continue;
//       }
//     }
    
//     return false;
//   } catch (e) {
//     debugPrint('❌ خطأ في فحص المستخدمين الفرعيين: $e');
//     return false;
//   }
// }

// /// فحص إعداد الملف الشخصي
// Future<bool> checkProfileSetup(String uid) async {
//   try {
//     final snapshot = await FirebaseDatabaseService.ref(uid)
//         .child('Personal Information')
//         .get();
        
//     if (snapshot.exists && snapshot.value != null) {
//       debugPrint('✅ تم العثور على معلومات شخصية');
//       return true;
//     } else {
//       debugPrint('ℹ️ لم يتم العثور على معلومات شخصية');
//       return false;
//     }
//   } catch (e) {
//     debugPrint('❌ خطأ في فحص إعداد الملف الشخصي: $e');
//     return false;
//   }
// }
