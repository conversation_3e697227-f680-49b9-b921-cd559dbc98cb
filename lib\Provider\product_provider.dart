import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/model/brands_model.dart';
import 'package:amrdev_win_pos/model/category_model.dart';
import 'package:amrdev_win_pos/model/unit_model.dart';

import '../Repository/product_repo.dart';
import '../model/product_model.dart';

ProductRepo _productRepo = ProductRepo(); // متغير غير مستخدم
final _productProvider = FutureProvider.autoDispose<List<ProductModel>>((ref) => productRepo.getAllProduct()); // متغير غير مستخدم
final _categoryProvider = FutureProvider.autoDispose<List<CategoryModel>>((ref) => productRepo.getAllCategory()); // متغير غير مستخدم
final _brandProvider = FutureProvider.autoDispose<List<BrandsModel>>((ref) => productRepo.getAllBrands()); // متغير غير مستخدم
final _unitProvider = FutureProvider.autoDispose<List<UnitModel>>((ref) => productRepo.getAllUnits()); // متغير غير مستخدم
