import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/sales_report.dart';

class SalesReportRepo {
  Future<List<SalesReport>> getAllSalesReport() async {
    List<SalesReport> salesReportList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Sales Report').orderByKey().get();
      for (var element in snapshot.children) {
        salesReportList.add(SalesReport.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب تقارير المبيعات: $e');
    }
    return salesReportList;
  }
}
