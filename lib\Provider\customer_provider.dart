import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../Repository/customer_repo.dart';
import '../model/customer_model.dart';

CustomerRepo _customerRepo = CustomerRepo(); // متغير غير مستخدم
final _buyerCustomerProvider = FutureProvider.autoDispose<List<CustomerModel>>((ref) => customerRepo.getAllBuyer()); // متغير غير مستخدم
final _allCustomerProvider = FutureProvider.autoDispose<List<CustomerModel>>((ref) => customerRepo.getAllCustomer()); // متغير غير مستخدم

final _supplierProvider = FutureProvider.autoDispose<List<CustomerModel>>((ref) => customerRepo.getAllSupplier()); // متغير غير مستخدم
