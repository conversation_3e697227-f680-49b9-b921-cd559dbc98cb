#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لحل الأخطاء النهائية المتبقية في مشروع Flutter
يركز على الأخطاء المحددة التي تم اكتشافها

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
import re
import sys
from pathlib import Path

class FinalErrorFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.changes_made = 0

    def fix_profile_setup(self):
        """إصلاح ملف profile_setup.dart"""
        file_path = self.project_path / "lib/Screen/Authentication/profile_setup.dart"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إزالة استخدام kIsWeb و ImagePickerWeb
            old_pattern = r'if \(kIsWeb\) \{\s*try \{\s*Uint8List\? bytesFromPicker = await ImagePickerWeb\.getImageAsBytes\(\);\s*// File\? file = await ImagePickerWeb\.getImageAsFile\(\);'
            new_pattern = '''try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        Uint8List bytesFromPicker = await pickedFile.readAsBytes();'''
            
            content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE | re.DOTALL)
            
            # إضافة الاستيرادات المطلوبة
            if "import 'package:image_picker/image_picker.dart';" not in content:
                content = content.replace(
                    "import 'package:image_picker_web/image_picker_web.dart';",
                    "import 'package:image_picker/image_picker.dart';"
                )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إصلاح {file_path.name}")
            self.changes_made += 1
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def fix_customer_files(self):
        """إصلاح ملفات العملاء"""
        customer_files = [
            "lib/Screen/Customer List/add_customer.dart",
            "lib/Screen/Customer List/edit_customer.dart"
        ]
        
        for file_path_str in customer_files:
            file_path = self.project_path / file_path_str
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إزالة if (kIsWeb) غير الضروري
                content = re.sub(r'if \(kIsWeb\) \{', '', content)
                
                # إصلاح الأقواس المتبقية
                content = re.sub(r'\s*\}\s*$', '', content, flags=re.MULTILINE)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إصلاح {file_path.name}")
                self.changes_made += 1
                
            except Exception as e:
                print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def fix_edit_product(self):
        """إصلاح ملف edit_product.dart"""
        file_path = self.project_path / "lib/Screen/Product/edit_product.dart"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إزالة استخدام kIsWeb
            content = re.sub(r'if \(kIsWeb\) \{', '', content)
            
            # إزالة التعليقات القديمة
            content = content.replace("// File? file = await ImagePickerWeb.getImageAsFile();", "")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إصلاح {file_path.name}")
            self.changes_made += 1
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def fix_settings_screen(self):
        """إصلاح ملف settings_screen.dart"""
        file_path = self.project_path / "lib/Screen/Settings/settings_screen.dart"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال ImagePickerWeb بـ ImagePicker
            old_pattern = r'if \(kIsWeb\) \{\s*try \{\s*Uint8List\? bytesFromPicker = await ImagePickerWeb\.getImageAsBytes\(\);\s*// File\? file = await ImagePickerWeb\.getImageAsFile\(\);'
            new_pattern = '''try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        Uint8List bytesFromPicker = await pickedFile.readAsBytes();'''
            
            content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE | re.DOTALL)
            
            # إضافة الاستيرادات المطلوبة
            if "import 'package:image_picker/image_picker.dart';" not in content:
                content = content.replace(
                    "import 'package:image_picker_web/image_picker_web.dart';",
                    "import 'package:image_picker/image_picker.dart';"
                )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إصلاح {file_path.name}")
            self.changes_made += 1
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def fix_add_item_popup(self):
        """إصلاح ملف add_item_popup.dart"""
        file_path = self.project_path / "lib/Screen/Widgets/Pop UP/Pos Sale/add_item_popup.dart"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # استبدال ImagePickerWeb بـ ImagePicker
            old_pattern = r'if \(kIsWeb\) \{\s*try \{\s*Uint8List\? bytesFromPicker = await ImagePickerWeb\.getImageAsBytes\(\);\s*// File\? file = await ImagePickerWeb\.getImageAsFile\(\);'
            new_pattern = '''try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        Uint8List bytesFromPicker = await pickedFile.readAsBytes();'''
            
            content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE | re.DOTALL)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إصلاح {file_path.name}")
            self.changes_made += 1
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def fix_commented_firebase_code(self):
        """إصلاح الكود المعلق الذي يحتوي على FirebaseDatabase"""
        files_to_fix = [
            "lib/Screen/Widgets/Pop UP/Pos Sale/create_customer_popup.dart",
            "lib/Screen/Widgets/Pop UP/Pos Sale/tab_create_customer_popup.dart",
            "lib/Screen/Widgets/Pop UP/Purchase/purchase_create_customer_popup.dart",
            "lib/Screen/Widgets/Pop UP/Purchase/tab_purchase_create_customer_popup.dart"
        ]
        
        for file_path_str in files_to_fix:
            file_path = self.project_path / file_path_str
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # إزالة الأسطر المعلقة التي تحتوي على FirebaseDatabase
                lines = content.split('\n')
                new_lines = []
                
                for line in lines:
                    if '//                                   final DatabaseReference _customerInformationRef = FirebaseDatabase.' in line:
                        continue  # تخطي هذا السطر
                    new_lines.append(line)
                
                content = '\n'.join(new_lines)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إصلاح {file_path.name}")
                self.changes_made += 1
                
            except Exception as e:
                print(f"❌ خطأ في إصلاح {file_path}: {e}")

    def run(self):
        """تشغيل جميع الإصلاحات"""
        print("🚀 بدء إصلاح الأخطاء النهائية...")
        
        self.fix_profile_setup()
        self.fix_customer_files()
        self.fix_edit_product()
        self.fix_settings_screen()
        self.fix_add_item_popup()
        self.fix_commented_firebase_code()
        
        print(f"\n✅ تم الانتهاء! تم إجراء {self.changes_made} إصلاح")

def main():
    fixer = FinalErrorFixer(".")
    fixer.run()

if __name__ == "__main__":
    main()
