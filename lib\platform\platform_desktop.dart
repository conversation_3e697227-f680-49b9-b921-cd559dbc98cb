// Desktop-specific implementations
import 'package:url_launcher/url_launcher.dart';

class PlatformHelper {
  static void setBeforeUnloadListener() {
    // No-op for desktop
  }
  
  static void redirectToUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
  
  static void downloadFile(String url, String filename) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  static void downloadBase64File(String base64Data, String filename) {
    // For desktop, we could save to file or show a dialog
    // For now, just ignore as this is mainly for web downloads
  }
  
  static void openInNewTab(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }
  
  static String getCurrentUrl() {
    return '';
  }
  
  static List<String> getPathSegments() {
    return [];
  }
}
