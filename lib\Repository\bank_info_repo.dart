
import 'dart:convert';
import '../model/bank_info_model.dart';
import '../services/firebase_database_service.dart';

class BankInfoRepo {
  Future<BankInfoModel> getPaypalInfo() async {
    DatabaseReference bankRef = FirebaseDatabaseService.ref('Admin Panel/Bank Info');
    final bankData = await bankRef.get();
    BankInfoModel bankInfoModel = BankInfoModel.fromJson(jsonDecode(jsonEncode(bankData.value)));

    return bankInfoModel;
  }
}
