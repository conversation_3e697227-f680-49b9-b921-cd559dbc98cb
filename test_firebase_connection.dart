import 'dart:convert';
import 'lib/services/firebase_database_service.dart';
//import 'package:flutter/foundation.dart';
// // import 'package:flutter/material.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'lib/firebase_options.dart';

// /// اختبار الاتصال بـ Firebase على Windows
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   try {
//     // تهيئة Firebase
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );
//     debugPrint('✅ تم تهيئة Firebase بنجاح');
    
//     // اختبار Firebase Auth
//     await testFirebaseAuth();
    
//     // اختبار Firebase Database
//     await testFirebaseDatabase();
    
//     debugPrint('🎉 جميع الاختبارات نجحت!');
    
//   } catch (e) {
//     debugPrint('❌ خطأ في الاختبار: $e');
//   }
// }

// /// اختبار Firebase Authentication
// Future<void> testFirebaseAuth() async {
//   debugPrint('\n🔐 اختبار Firebase Authentication...');
  
//   try {
//     // التحقق من حالة المستخدم الحالي
//     final currentUser = FirebaseAuth.instance.currentUser;
//     if (currentUser != null) {
//       debugPrint('✅ المستخدم مسجل دخول: ${currentUser.email}');
      
//       // اختبار الحصول على ID Token
//       final idToken = await currentUser.getIdToken();
//       debugPrint('✅ تم الحصول على ID Token بنجاح');
      
//     } else {
//       debugPrint('ℹ️ لا يوجد مستخدم مسجل دخول حالياً');
//     }
    
//   } catch (e) {
//     debugPrint('❌ خطأ في Firebase Auth: $e');
//     throw e;
//   }
// }

// /// اختبار Firebase Database Service
// Future<void> testFirebaseDatabase() async {
//   debugPrint('\n🗄️ اختبار Firebase Database Service...');
  
//   try {
//     // اختبار إنشاء مرجع قاعدة البيانات
//     final dbRef = FirebaseDatabaseService.ref('test');
//     debugPrint('✅ تم إنشاء مرجع قاعدة البيانات');
    
//     // اختبار كتابة البيانات
//     final testData = {
//       'message': 'اختبار الاتصال من Windows',
//       'timestamp': DateTime.now().millisecondsSinceEpoch,
//       'platform': 'Windows',
//     };
    
//     await dbRef.child('connection_test').set(testData);
//     debugPrint('✅ تم كتابة البيانات بنجاح');
    
//     // اختبار قراءة البيانات
//     final snapshot = await dbRef.child('connection_test').get();
//     if (snapshot.exists) {
//       final data = snapshot.value;
//       debugPrint('✅ تم قراءة البيانات بنجاح: ${jsonEncode(data)}');
//     } else {
//       debugPrint('⚠️ لم يتم العثور على البيانات');
//     }
    
//     // اختبار حذف البيانات
//     await dbRef.child('connection_test').remove();
//     debugPrint('✅ تم حذف البيانات بنجاح');
    
//     // اختبار قراءة بيانات PayPal (كما في التطبيق الأصلي)
//     await testPaypalInfo();
    
//   } catch (e) {
//     debugPrint('❌ خطأ في Firebase Database: $e');
//     throw e;
//   }
// }

// /// اختبار قراءة معلومات PayPal
// Future<void> testPaypalInfo() async {
//   debugPrint('\n💳 اختبار قراءة معلومات PayPal...');
  
//   try {
//     final paypalRef = FirebaseDatabaseService.ref('Admin Panel/Paypal Info');
//     final paypalData = await paypalRef.get();
    
//     if (paypalData.exists && paypalData.value != null) {
//       debugPrint('✅ تم العثور على معلومات PayPal');
//       final data = paypalData.value;
//       if (data is Map) {
//         print('📊 البيانات المتاحة: ${data.keys.join(', ')}');
//       }
//     } else {
//       debugPrint('ℹ️ لم يتم العثور على معلومات PayPal في قاعدة البيانات');
//     }
    
//   } catch (e) {
//     debugPrint('❌ خطأ في قراءة معلومات PayPal: $e');
//     // لا نرمي الخطأ هنا لأن هذا اختبار اختياري
//   }
// }
