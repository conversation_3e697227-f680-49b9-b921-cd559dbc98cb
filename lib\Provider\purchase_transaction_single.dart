import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/Provider/transactions_provider.dart';
import 'package:amrdev_win_pos/model/sale_transaction_model.dart';

import '../Repository/transactions_repo.dart';
import '../Repository/transactions_repo.dart';
import '../Repository/transactions_repo.dart';
import '../model/purchase_transation_model.dart';

PurchaseTransitionRepo _purchaseTransitionSingleRepo = PurchaseTransitionRepo(); // متغير غير مستخدم
final _purchaseTransitionProviderSIngle = FutureProvider.autoDispose<List<PurchaseTransactionModel>>((ref) => purchaseTransitionRepo.getAllTransitionSingle()); // متغير غير مستخدم