#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل نهائي لمشروع Flutter POS
يتأكد من أن جميع الإصلاحات تمت بنجاح والمشروع جاهز للتشغيل

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
import re
import sys
from pathlib import Path

class ProjectTester:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.test_results = {
            'total_files': 0,
            'files_with_issues': 0,
            'critical_errors': 0,
            'warnings': 0,
            'success': True
        }

    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 فحص ملفات المشروع...")
        
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        self.test_results['total_files'] = len(self.dart_files)
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")

    def test_firebase_integration(self):
        """اختبار تكامل Firebase"""
        print("\n🔥 اختبار تكامل Firebase...")
        
        firebase_issues = []
        
        for file_path in self.dart_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استخدام FirebaseDatabase القديم
                if 'FirebaseDatabase.instance' in content and 'FirebaseDatabaseService' not in content:
                    firebase_issues.append(f"❌ {file_path.name}: استخدام FirebaseDatabase القديم")
                
                # فحص وجود FirebaseDatabaseService
                if 'FirebaseDatabaseService' in content:
                    if ("firebase_database_service.dart" not in content and
                        "import '../../services/firebase_database_service.dart';" not in content and
                        "import '../services/firebase_database_service.dart';" not in content and
                        "import 'services/firebase_database_service.dart';" not in content):
                        firebase_issues.append(f"⚠️ {file_path.name}: استخدام FirebaseDatabaseService بدون استيراد")
                
            except Exception as e:
                firebase_issues.append(f"❌ خطأ في فحص {file_path.name}: {e}")
        
        if firebase_issues:
            print("❌ مشاكل Firebase:")
            for issue in firebase_issues[:10]:  # عرض أول 10 مشاكل فقط
                print(f"  {issue}")
            if len(firebase_issues) > 10:
                print(f"  ... و {len(firebase_issues) - 10} مشكلة أخرى")
            self.test_results['critical_errors'] += len(firebase_issues)
        else:
            print("✅ تكامل Firebase سليم")

    def test_imports(self):
        """اختبار الاستيرادات"""
        print("\n📦 اختبار الاستيرادات...")
        
        import_issues = []
        
        for file_path in self.dart_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استخدام jsonDecode/jsonEncode بدون dart:convert
                if ('jsonDecode' in content or 'jsonEncode' in content) and "import 'dart:convert';" not in content:
                    import_issues.append(f"❌ {file_path.name}: استخدام JSON بدون dart:convert")
                
                # فحص استخدام ImagePickerWeb القديم
                if 'ImagePickerWeb' in content:
                    import_issues.append(f"❌ {file_path.name}: استخدام ImagePickerWeb القديم")
                
                # فحص استخدام Uint8List بدون استيراد
                if 'Uint8List' in content and "import 'dart:typed_data';" not in content and "import 'package:flutter/services.dart';" not in content:
                    import_issues.append(f"⚠️ {file_path.name}: استخدام Uint8List بدون استيراد مناسب")
                
            except Exception as e:
                import_issues.append(f"❌ خطأ في فحص {file_path.name}: {e}")
        
        if import_issues:
            print("❌ مشاكل الاستيرادات:")
            for issue in import_issues[:10]:
                print(f"  {issue}")
            if len(import_issues) > 10:
                print(f"  ... و {len(import_issues) - 10} مشكلة أخرى")
            self.test_results['critical_errors'] += len(import_issues)
        else:
            print("✅ الاستيرادات سليمة")

    def test_image_picker(self):
        """اختبار Image Picker"""
        print("\n🖼️ اختبار Image Picker...")
        
        picker_issues = []
        
        for file_path in self.dart_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص استخدام kIsWeb مع ImagePicker (غير ضروري لـ Windows)
                if 'if (kIsWeb)' in content and 'ImagePicker' in content:
                    picker_issues.append(f"⚠️ {file_path.name}: استخدام kIsWeb مع ImagePicker (غير ضروري)")
                
                # فحص استخدام ImagePicker الصحيح
                if 'ImagePicker' in content and 'XFile' not in content:
                    picker_issues.append(f"⚠️ {file_path.name}: استخدام ImagePicker بدون XFile")
                
            except Exception as e:
                picker_issues.append(f"❌ خطأ في فحص {file_path.name}: {e}")
        
        if picker_issues:
            print("⚠️ تحذيرات Image Picker:")
            for issue in picker_issues[:5]:
                print(f"  {issue}")
            if len(picker_issues) > 5:
                print(f"  ... و {len(picker_issues) - 5} تحذير آخر")
            self.test_results['warnings'] += len(picker_issues)
        else:
            print("✅ Image Picker محدث ومتوافق")

    def test_currency_and_language(self):
        """اختبار العملة واللغة"""
        print("\n🌍 اختبار العملة واللغة...")
        
        locale_issues = []
        
        for file_path in self.dart_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص العملة
                if "currency: '\\$'" in content:
                    locale_issues.append(f"⚠️ {file_path.name}: استخدام عملة خاطئة ($)")
                
                # فحص اللغة
                if "currentLocale: 'en'" in content:
                    locale_issues.append(f"⚠️ {file_path.name}: استخدام لغة خاطئة (en)")
                
            except Exception as e:
                locale_issues.append(f"❌ خطأ في فحص {file_path.name}: {e}")
        
        if locale_issues:
            print("⚠️ مشاكل العملة واللغة:")
            for issue in locale_issues:
                print(f"  {issue}")
            self.test_results['warnings'] += len(locale_issues)
        else:
            print("✅ العملة واللغة محددة بشكل صحيح")

    def check_critical_files(self):
        """فحص الملفات الحرجة"""
        print("\n🎯 فحص الملفات الحرجة...")
        
        critical_files = [
            "lib/main.dart",
            "lib/services/firebase_database_service.dart",
            "lib/const.dart",
            "pubspec.yaml"
        ]
        
        missing_files = []
        
        for file_path_str in critical_files:
            file_path = self.project_path / file_path_str
            if not file_path.exists():
                missing_files.append(file_path_str)
        
        if missing_files:
            print("❌ ملفات حرجة مفقودة:")
            for file in missing_files:
                print(f"  {file}")
            self.test_results['critical_errors'] += len(missing_files)
            self.test_results['success'] = False
        else:
            print("✅ جميع الملفات الحرجة موجودة")

    def run_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء الاختبار الشامل للمشروع...")
        print("="*60)
        
        self.find_dart_files()
        self.check_critical_files()
        self.test_firebase_integration()
        self.test_imports()
        self.test_image_picker()
        self.test_currency_and_language()
        
        self.print_final_report()

    def print_final_report(self):
        """طباعة التقرير النهائي"""
        print("\n" + "="*60)
        print("📊 التقرير النهائي")
        print("="*60)
        
        print(f"📁 إجمالي الملفات: {self.test_results['total_files']}")
        print(f"❌ أخطاء حرجة: {self.test_results['critical_errors']}")
        print(f"⚠️ تحذيرات: {self.test_results['warnings']}")
        
        if self.test_results['critical_errors'] == 0:
            print("\n🎉 ممتاز! المشروع جاهز للتشغيل!")
            print("✅ لا توجد أخطاء حرجة")
            
            if self.test_results['warnings'] == 0:
                print("✅ لا توجد تحذيرات")
                print("\n🚀 يمكنك تشغيل المشروع الآن باستخدام:")
                print("   flutter run -d windows")
            else:
                print(f"⚠️ يوجد {self.test_results['warnings']} تحذير (غير حرج)")
                print("\n🚀 المشروع قابل للتشغيل مع وجود تحذيرات بسيطة")
        else:
            print(f"\n❌ يوجد {self.test_results['critical_errors']} خطأ حرج يجب إصلاحه")
            print("🔧 يُنصح بإصلاح الأخطاء الحرجة قبل التشغيل")
        
        print("\n" + "="*60)

def main():
    tester = ProjectTester(".")
    tester.run_tests()

if __name__ == "__main__":
    main()
