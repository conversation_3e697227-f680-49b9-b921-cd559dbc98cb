import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../platform/platform_helper.dart';

class InvoiceRedirectPage extends StatefulWidget {
  const InvoiceRedirectPage({super.key});

  @override
  _InvoiceRedirectPageState createState() => _InvoiceRedirectPageState();
}

class _InvoiceRedirectPageState extends State<InvoiceRedirectPage> {
  @override
  void initState() {
    super.initState();
    _redirectToInvoice();
  }

  Future<void> _redirectToInvoice() async {
    // Get the current URL path segments
    List<String> pathSegments = PlatformHelper.getPathSegments();

    // Check if the URL matches the /invoices/invoiceNumber pattern
    if (pathSegments.length >= 2 && pathSegments[0] == 'invoices') {
      String invoiceNumber = pathSegments[2];
      String type = pathSegments[1];
      try {
        // Generate the Firebase Storage path based on the invoice number
        String path = '$type/invoice-$invoiceNumber.pdf';

        // Get the download URL from Firebase Storage
        FirebaseStorage storage = FirebaseStorage.instance;
        String downloadUrl = await storage.ref(path).getDownloadURL();

        // Redirect the user to the download URL
        PlatformHelper.redirectToUrl(downloadUrl);
      } catch (e) {
        debugPrint('Error fetching download link: $e');
        // Optionally, redirect to an error page or show an alert
      }
    } else {
      // If the URL is invalid, you could redirect to a 404 page or home
      PlatformHelper.redirectToUrl('/404');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Optionally, return a loader or empty container while redirecting
    return Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }
}