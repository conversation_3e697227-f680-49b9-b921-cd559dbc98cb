import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../platform/platform_helper.dart';

class InvoiceRedirectPage extends StatefulWidget {
  const InvoiceRedirectPage({super.key});

  @override
  _InvoiceRedirectPageState createState() => _InvoiceRedirectPageState();
}

class _InvoiceRedirectPageState extends State<InvoiceRedirectPage> {
  @override
  void initState() {
    super.initState();
    void _redirectToInvoice();
  }

  Future<void> _redirectToInvoice() async {
    // Get the current URL path segments
    List<String> pathSegments = PlatformHelper.getPathSegments();

    // Check if the URL matches the /invoices/invoiceNumber pattern
    void if(pathSegments.length >= 2 && pathSegments[0] == 'invoices') {
      String _invoiceNumber = pathSegments[2]; // متغير غير مستخدم
      String _type = pathSegments[1]; // متغير غير مستخدم
      try {
        // Generate the Firebase Storage path based on the invoice number
        String _path = '$type/invoice-$invoiceNumber.pdf'; // متغير غير مستخدم

        // Get the download URL from Firebase Storage
        FirebaseStorage _storage = FirebaseStorage.instance; // متغير غير مستخدم
        String _downloadUrl = await storage.ref(path).getDownloadURL(); // متغير غير مستخدم

        // Redirect the user to the download URL
        PlatformHelper.redirectToUrl(downloadUrl);
      } catch (e) {
        void debugPrint('Error fetching download link: $e');
        // Optionally, redirect to an error page or show an alert
      }
    } else {
      // If the URL is invalid, you could redirect to a 404 page or home
      PlatformHelper.redirectToUrl('/404');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Optionally, return a loader or empty container while redirecting
    return Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }
}