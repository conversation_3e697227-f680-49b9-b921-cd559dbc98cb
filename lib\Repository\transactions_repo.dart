import 'package:flutter/foundation.dart';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/due_transaction_model.dart';
import '../model/purchase_transation_model.dart';
import '../model/sale_transaction_model.dart';

class TransitionRepo {
  Future<List<SaleTransactionModel>> getAllTransition() async {
    List<SaleTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Sales Transition').orderByKey().get();
      for (var element in snapshot.children) {
        SaleTransactionModel data = SaleTransactionModel.fromJson(jsonDecode(jsonEncode(element.value)));
        data.key = element.key;
        transitionList.add(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المبيعات: $e');
    }
    return transitionList;
  }
}

class PurchaseTransitionRepo {
  Future<List<dynamic>> getAllTransition() async {
    List<dynamic> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Purchase Transition').orderByKey().get();
      for (var element in snapshot.children) {
        PurchaseTransactionModel data = PurchaseTransactionModel.fromJson(jsonDecode(jsonEncode(element.value)));
        data.key = element.key;
        transitionList.add(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المشتريات: $e');
    }
    return transitionList;
  }
  Future<List<PurchaseTransactionModel>> getAllTransitionSingle() async {
    List<PurchaseTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Purchase Transition').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(PurchaseTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المشتريات الفردية: $e');
    }
    return transitionList;
  }
}

class DueTransitionRepo {
  Future<List<DueTransactionModel>> getAllTransition() async {
    List<DueTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Due Transaction').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(DueTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات المستحقات: $e');
    }
    return transitionList;
  }
}

class QuotationRepo {
  Future<List<SaleTransactionModel>> getAllQuotation() async {
    List<SaleTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Sales Quotation').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(SaleTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب بيانات عروض الأسعار: $e');
    }
    return transitionList;
  }
}

class QuotationHistoryRepo {
  Future<List<SaleTransactionModel>> getAllQuotationHistory() async {
    List<SaleTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Quotation Convert History').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(SaleTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب تاريخ عروض الأسعار: $e');
    }
    return transitionList;
  }
}
