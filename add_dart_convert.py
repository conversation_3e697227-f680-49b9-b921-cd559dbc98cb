#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بسيط لإضافة import 'dart:convert'; للملفات التي تحتاجه
"""

import os
import re
from pathlib import Path

def add_dart_convert_to_file(file_path):
    """إضافة dart:convert إذا كان الملف يحتاجه ولا يحتويه"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحقق إذا كان الملف يحتاج dart:convert
        needs_convert = any(func in content for func in ['jsonDecode', 'jsonEncode', 'json.decode', 'json.encode'])
        
        # تحقق إذا كان dart:convert موجود بالفعل
        has_convert = "import 'dart:convert';" in content
        
        if needs_convert and not has_convert:
            # البحث عن أول import
            import_match = re.search(r'^import\s+[\'"][^\'"]+[\'"];', content, re.MULTILINE)
            
            if import_match:
                # إدراج dart:convert قبل أول import
                insert_pos = import_match.start()
                new_content = content[:insert_pos] + "import 'dart:convert';\n" + content[insert_pos:]
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ تم إضافة dart:convert إلى {file_path.name}")
                return True
            else:
                # إذا لم يوجد imports، أضف في البداية
                new_content = "import 'dart:convert';\n" + content
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ تم إضافة dart:convert إلى {file_path.name}")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {e}")
        return False

def main():
    """تشغيل السكريبت"""
    print("🔧 إضافة dart:convert للملفات التي تحتاجه...")
    
    # قائمة الملفات التي تحتاج dart:convert
    files_to_fix = [
        "lib/debug/login_debug.dart",
        "lib/Repository/sales_report_repo.dart",
        "lib/Screen/tax rates/tax_model.dart",
        "lib/Screen/Subscription/subscript.dart",
        "lib/Repository/shop_category_repo.dart",
        "lib/Repository/get_user_role_repo.dart",
        "lib/Repository/bank_info_repo.dart",
        "lib/subscription.dart",
        "lib/currency.dart",
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        path = Path(file_path)
        if path.exists():
            if add_dart_convert_to_file(path):
                fixed_count += 1
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")
    
    print(f"\n🎉 تم الانتهاء! تم إصلاح {fixed_count} ملف")

if __name__ == "__main__":
    main()
