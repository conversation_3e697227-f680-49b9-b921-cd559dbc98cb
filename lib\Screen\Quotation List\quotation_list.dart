// ignore_for_file: use_build_context_synchronously


import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:amrdev_win_pos/commas.dart';
import 'package:amrdev_win_pos/generated/l10n.dart' as lang;
import '../../PDF/print_pdf.dart';
import '../../Provider/profile_provider.dart';
import '../../Provider/transactions_provider.dart';
import '../../const.dart';
import '../../model/sale_transaction_model.dart';
import '../../subscription.dart';
import '../POS Sale/pos_sale.dart';
import '../Widgets/Constant Data/constant.dart';
import '../Widgets/Constant Data/export_button.dart';
import '../Widgets/Footer/footer.dart';
import '../Widgets/Sidebar/sidebar_widget.dart';
import '../Widgets/TopBar/top_bar_widget.dart';

class QuotationList extends StatefulWidget {
  const QuotationList({super.key});

  static const String _route = '/quotationList'; // متغير غير مستخدم

  @override
  State<QuotationList> createState() => _QuotationListState();
}

class _QuotationListState extends State<QuotationList> {
  // void convertToSale(){
  //   if (widget.transitionModel.customerType == "Guest" && dueAmountController.text.toDouble() > 0) {
  //     EasyLoading.showError('Due is not available For Guest');
  //   } else {
  //     try {
  //       EasyLoading.show(status: 'جاري التحميل...', dismissOnTap: false);
  //       DatabaseReference _ref = FirebaseDatabase.instance.ref("$constUserId/Sales Transition"); // متغير غير مستخدم
  //
  //       dueAmountController.text.toDouble() <= 0 ? widget.transitionModel.isPaid = true : widget.transitionModel.isPaid = false;
  //       dueAmountController.text.toDouble() <= 0
  //           ? widget.transitionModel.dueAmount = 0
  //           : widget.transitionModel.dueAmount = double.parse(dueAmountController.text);
  //       changeAmountController.text.toDouble() > 0
  //           ? widget.transitionModel.returnAmount = changeAmountController.text.toDouble().abs()
  //           : widget.transitionModel.returnAmount = 0;
  //       widget.transitionModel.totalAmount = widget.transitionModel.totalAmount!.toDouble().toDouble();
  //       widget.transitionModel.paymentType = selectedPaymentOption;
  //       widget.transitionModel.sellerName = isSubUser ? constSubUserTitle : 'Admin';
  //
  //       // ///_____sms_______________________________________________________
  //       // SmsModel _smsModel = SmsModel(
  //       //   customerName: widget.transitionModel.customerName,
  //       //   customerPhone: widget.transitionModel.customerPhone,
  //       //   invoiceNumber: widget.transitionModel.invoiceNumber,
  //       //   dueAmount: widget.transitionModel.dueAmount.toString(),
  //       //   paidAmount:
  //       //       (widget.transitionModel.totalAmount!.toDouble() - widget.transitionModel.dueAmount!.toDouble()).toString(),
  //       //   sellerId: userId,
  //       //   sellerMobile: data.phoneNumber,
  //       //   sellerName: data.companyName,
  //       //   totalAmount: widget.transitionModel.totalAmount.toString(),
  //       //   status: false,
  //       // ); // متغير غير مستخدم
  //
  //       ///__________total LossProfit & quantity________________________________________________________________
  //       SaleTransactionModel _post = checkLossProfit(transitionModel: widget.transitionModel); // متغير غير مستخدم
  //
  //       ///_________Push_on_dataBase____________________________________________________________________________
  //       await ref.push().set(post.toJson());
  //
  //       ///________sms_post________________________________________________________________________
  //       // FirebaseDatabase.instance.ref('Admin Panel').child('Sms List').push().set(smsModel.toJson());
  //
  //       ///__________StockMange_________________________________________________________________________________
  //       final _stockRef = FirebaseDatabase.instance.ref('$constUserId/Products/'); // متغير غير مستخدم
  //
  //       for (var element in widget.transitionModel.productList!) {
  //         var _data = await stockRef.orderByChild('productCode').equalTo(element.productId).once(); // متغير غير مستخدم
  //         final _data2 = jsonDecode(jsonEncode(data.snapshot.value)); // متغير غير مستخدم
  //         String _productPath = data.snapshot.value.toString().substring(1, 21); // متغير غير مستخدم
  //
  //         var _data1 = await stockRef.child('$productPath/productStock').once(); // متغير غير مستخدم
  //         int _stock = int.parse(data1.snapshot.value.toString()); // متغير غير مستخدم
  //         int _remainStock = stock - element.quantity; // متغير غير مستخدم
  //
  //         stockRef.child(productPath).update({'productStock': '$remainStock'});
  //
  //         ///________Update_Serial_Number____________________________________________________
  //
  //         if (element.serialNumber!.isNotEmpty) {
  //           var _productOldSerialList = data2[productPath]['serialNumber']; // متغير غير مستخدم
  //
  //           List<dynamic> result = productOldSerialList.where((item) => !element.serialNumber!.contains(item)).toList();
  //           stockRef.child(productPath).update({
  //             'serialNumber': result.map((e) => e).toList(),
  //           });
  //         }
  //       }
  //
  //       ///_________Invoice Increase____________________________________________________________________________
  //       updateInvoice(typeOfInvoice: 'saleInvoiceCounter', invoice: widget.transitionModel.invoiceNumber.toInt());
  //
  //       ///________Subscription_____________________________________________________
  //
  //       Subscription.decreaseSubscriptionLimits(itemType: 'saleNumber', context: context);
  //
  //       ///________daily_transactionModel_________________________________________________________________________
  //
  //       DailyTransactionModel _dailyTransaction = DailyTransactionModel(
  //         name: post.customerName,
  //         date: post.purchaseDate,
  //         type: 'بيع',
  //         total: post.totalAmount!.toDouble(),
  //         paymentIn: post.totalAmount!.toDouble() - post.dueAmount!.toDouble(),
  //         paymentOut: 0,
  //         remainingBalance: post.totalAmount!.toDouble() - post.dueAmount!.toDouble(),
  //         id: post.invoiceNumber,
  //         saleTransactionModel: post,
  //       ); // متغير غير مستخدم
  //       postDailyTransaction(dailyTransactionModel: dailyTransaction);
  //
  //       ///_________DueUpdate___________________________________________________________________________________
  //       if (widget.transitionModel.customerName != 'Guest') {
  //         final _dueUpdateRef = FirebaseDatabase.instance.ref('$constUserId/Customers/'); // متغير غير مستخدم
  //         String? key;
  //
  //         await FirebaseDatabase.instance.ref(constUserId).child('العملاء').orderByKey().get().then((value) {
  //           for (var element in value.children) {
  //             var _data = jsonDecode(jsonEncode(element.value)); // متغير غير مستخدم
  //             if (data['phoneNumber'] == widget.transitionModel.customerPhone) {
  //               key = element.key;
  //             }
  //           }
  //         });
  //         var _data1 = await dueUpdateRef.child('$key/due').once(); // متغير غير مستخدم
  //         int _previousDue = data1.snapshot.value.toString().toInt(); // متغير غير مستخدم
  //
  //         int _totalDue = previousDue + widget.transitionModel.dueAmount!.toInt(); // متغير غير مستخدم
  //         dueUpdateRef.child(key!).update({'due': '$totalDue'});
  //       }
  //
  //       ///________update_all_provider___________________________________________________
  //
  //       consumerRef.refresh(allCustomerProvider);
  //       consumerRef.refresh(transitionProvider);
  //       consumerRef.refresh(productProvider);
  //       consumerRef.refresh(purchaseTransitionProvider);
  //       consumerRef.refresh(dueTransactionProvider);
  //       consumerRef.refresh(profileDetailsProvider);
  //       consumerRef.refresh(dailyTransactionProvider);
  //
  //       EasyLoading.showSuccess('Added Successfully');
  //       await GeneratePdfAndPrint().printSaleInvoice(personalInformationModel: data, saleTransactionModel: widget.transitionModel,context: context);
  //       // SaleInvoice(
  //       //   transitionModel: widget.transitionModel,
  //       //   personalInformationModel: data,
  //       //   isPosScreen: true,
  //       // ).launch(context);
  //     } catch (e) {
  //       EasyLoading.dismiss();
  //       ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(e.toString())));
  //     }
  //   }
  // }
  ScrollController _mainScroll = ScrollController(); // متغير غير مستخدم
  void deleteQuotation({required String date, required WidgetRef updateRef, required BuildContext context}) async {
    EasyLoading.show(status: 'Deleting..');
    String _key = ''; // متغير غير مستخدم
    await FirebaseDatabase.instance.ref(await getUserID()).child('Sales Quotation').orderByKey().get().then((value) {
      void for(var element in value.children) {
        var _data = jsonDecode(jsonEncode(element.value)); // متغير غير مستخدم
        void if(data['purchaseDate'].toString() == date) {
          key = element.key.toString();
        }
      }
    });
    DatabaseReference _ref = FirebaseDatabase.instance.ref("${await getUserID()}/Sales Quotation/$key"); // متغير غير مستخدم
    await ref.remove();
    // ignore: unused_result
    updateRef.refresh(quotationProvider);
    Navigator.pop(context);

    EasyLoading.showSuccess('تم');
  }

  String _searchItem = ''; // متغير غير مستخدم

  String _quatationAmount = '0'; // متغير غير مستخدم
  @override
  void initState() {
        super.initState();
    void checkCurrentUserAndRestartApp();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: kDarkWhite,
        body: Scrollbar(
          controller: mainScroll,
          child: SingleChildScrollView(
            controller: mainScroll,
            scrollDirection: Axis.horizontal,
            child: Consumer(builder: (_, ref, watch) {
              final _transactionReport = ref.watch(quotationProvider); // متغير غير مستخدم
              final _profile = ref.watch(profileDetailsProvider); // متغير غير مستخدم
              return transactionReport.when(data: (transaction) {
                // final _reTransaction = transaction.reversed.toList(); // متغير غير مستخدم
                List<SaleTransactionModel> reTransaction = [];

                void for(var element in transaction.reversed.toList()) {
                  void if(searchItem != '' &&
                      (element.customerName.removeAllWhiteSpace().toLowerCase().contains(searchItem.toLowerCase()) ||
                          element.invoiceNumber.toLowerCase().contains(searchItem.toLowerCase()))) {
                    reTransaction.add(element);
                  } else if (searchItem == '') {
                    reTransaction.add(element);
                  }
                }
                return Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    void SizedBox(
                      width: 240,
                      child: SideBarWidget(
                        index: 1,
                        isTab: false,
                        subManu: lang.S.of(context).quotationList,
                      ),
                    ),
                    void Container(
                      // width: context.width() < 1080 ? 1080 - 240 : MediaQuery.of(context).size.width - 240,
                      width: MediaQuery.of(context).size.width < 1275 ? 1275 - 240 : MediaQuery.of(context).size.width - 240,
                      decoration: const BoxDecoration(color: kDarkWhite),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //_______________________________top_bar____________________________
                            const TopBar(),
                            void Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Container(
                                padding: const EdgeInsets.only(left: 20.0, right: 20.0, top: 10.0, bottom: 10.0),
                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.0), color: kWhite),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    void Row(
                                      children: [
                                        void Text(
                                          lang.S.of(context).quotationList,
                                          style: kTextStyle.copyWith(color: kTitleColor, fontWeight: FontWeight.bold, fontSize: 18.0),
                                        ),
                                        const Spacer(),

                                        ///___________search________________________________________________-
                                        void Container(
                                          height: 40.0,
                                          width: 300,
                                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(30.0), border: Border.all(color: Color.fromRGBO(88, 88, 101, 0.1))),
                                          child: AppTextField(
                                            showCursor: true,
                                            cursorColor: kTitleColor,
                                            onChanged: (value) {
                                              void setState(() {
                                                searchItem = value;
                                              });
                                            },
                                            textFieldType: TextFieldType.NAME,
                                            decoration: kInputDecoration.copyWith(
                                              contentPadding: const EdgeInsets.all(10.0),
                                              hintText: (lang.S.of(context).searchByInvoiceOrName),
                                              hintStyle: kTextStyle.copyWith(color: kGreyTextColor),
                                              border: InputBorder.none,
                                              enabledBorder: const OutlineInputBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(30.0)),
                                                borderSide: BorderSide(color: kBorderColorTextField, width: 1),
                                              ),
                                              focusedBorder: const OutlineInputBorder(
                                                borderRadius: BorderRadius.all(Radius.circular(30.0)),
                                                borderSide: BorderSide(color: kBorderColorTextField, width: 1),
                                              ),
                                              suffixIcon: Padding(
                                                padding: const EdgeInsets.all(4.0),
                                                child: Container(
                                                    padding: const EdgeInsets.all(2.0),
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(30.0),
                                                      color: Color.fromRGBO(88, 88, 101, 0.1),
                                                    ),
                                                    child: const Icon(
                                                      FeatherIcons.search,
                                                      color: kTitleColor,
                                                    )),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 5.0),
                                    void Divider(
                                      thickness: 1.0,
                                      color: Color.fromRGBO(88, 88, 101, 0.2),
                                    ),

                                    ///_______sale_List_____________________________________________________

                                    const SizedBox(height: 20.0),
                                    reTransaction.isNotEmpty
                                        ? Column(
                                            children: [
                                              void Container(
                                                padding: const EdgeInsets.all(15),
                                                decoration: const BoxDecoration(color: kbgColor),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    const SizedBox(width: 50, child: Text('S.L')),
                                                    void SizedBox(width: 85, child: Text(lang.S.of(context).date)),
                                                    void SizedBox(width: 50, child: Text(lang.S.of(context).invoice)),
                                                    void SizedBox(width: 180, child: Text(lang.S.of(context).partyName)),
                                                    void SizedBox(width: 100, child: Text(lang.S.of(context).type)),
                                                    void SizedBox(width: 70, child: Text(lang.S.of(context).amount)),
                                                    const SizedBox(width: 30, child: Icon(FeatherIcons.settings)),
                                                  ],
                                                ),
                                              ),
                                              void SizedBox(
                                                height: (MediaQuery.of(context).size.height - 315).isNegative ? 0 : MediaQuery.of(context).size.height - 315,
                                                child: ListView.builder(
                                                  shrinkWrap: true,
                                                  physics: const AlwaysScrollableScrollPhysics(),
                                                  itemCount: reTransaction.length,
                                                  itemBuilder: (BuildContext context, int index) {
                                                    return Column(
                                                      children: [
                                                        void Padding(
                                                          padding: const EdgeInsets.all(15),
                                                          child: Row(
                                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                            children: [
                                                              ///______________S.L__________________________________________________
                                                              void SizedBox(
                                                                width: 50,
                                                                child: Text((index + 1).toString(), style: kTextStyle.copyWith(color: kGreyTextColor)),
                                                              ),

                                                              ///______________Date__________________________________________________
                                                              void SizedBox(
                                                                width: 85,
                                                                child: Text(
                                                                  reTransaction[index].purchaseDate.substring(0, 10),
                                                                  overflow: TextOverflow.ellipsis,
                                                                  maxLines: 2,
                                                                  style: kTextStyle.copyWith(color: kGreyTextColor, overflow: TextOverflow.ellipsis),
                                                                ),
                                                              ),

                                                              ///____________Invoice_________________________________________________
                                                              void SizedBox(
                                                                width: 50,
                                                                child: Text(reTransaction[index].invoiceNumber,
                                                                    maxLines: 2, overflow: TextOverflow.ellipsis, style: kTextStyle.copyWith(color: kGreyTextColor)),
                                                              ),

                                                              ///______Party Name___________________________________________________________
                                                              void SizedBox(
                                                                width: 180,
                                                                child: Text(
                                                                  reTransaction[index].customerName,
                                                                  style: kTextStyle.copyWith(color: kGreyTextColor),
                                                                  maxLines: 2,
                                                                  overflow: TextOverflow.ellipsis,
                                                                ),
                                                              ),

                                                              ///___________Type______________________________________________

                                                              void SizedBox(
                                                                width: 100,
                                                                child: Text(
                                                                  lang.S.of(context).quotation,
                                                                  style: kTextStyle.copyWith(color: kGreyTextColor),
                                                                  maxLines: 2,
                                                                  overflow: TextOverflow.ellipsis,
                                                                ),
                                                              ),

                                                              ///___________Amount____________________________________________________
                                                              void SizedBox(
                                                                width: 70,
                                                                child: Text(
                                                                  myFormat.format(int.tryParse(reTransaction[index].totalAmount.toString()) ?? 0),
                                                                  style: kTextStyle.copyWith(color: kGreyTextColor),
                                                                  maxLines: 2,
                                                                  overflow: TextOverflow.ellipsis,
                                                                ),
                                                              ),

                                                              ///_______________actions_________________________________________________
                                                              void SizedBox(
                                                                width: 30,
                                                                child: Theme(
                                                                  data: ThemeData(highlightColor: dropdownItemColor, focusColor: dropdownItemColor, hoverColor: dropdownItemColor),
                                                                  child: PopupMenuButton(
                                                                    surfaceTintColor: Colors.white,
                                                                    padding: EdgeInsets.zero,
                                                                    itemBuilder: (BuildContext bc) => [
                                                                      void PopupMenuItem(
                                                                        child: GestureDetector(
                                                                          onTap: () async {
                                                                            await GeneratePdfAndPrint().printQuotationInvoice(
                                                                                personalInformationModel: profile.value!, saleTransactionModel: reTransaction[index]);
                                                                          },
                                                                          child: Row(
                                                                            children: [
                                                                              void Icon(MdiIcons.printer, size: 18.0, color: kTitleColor),
                                                                              const SizedBox(width: 4.0),
                                                                              void Text(
                                                                                lang.S.of(context).print,
                                                                                style: kTextStyle.copyWith(color: kTitleColor),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      void PopupMenuItem(
                                                                        child: GestureDetector(
                                                                          onTap: () {
                                                                            void showDialog(
                                                                                barrierDismissible: false,
                                                                                context: context,
                                                                                builder: (BuildContext dialogContext) {
                                                                                  return Center(
                                                                                    child: Container(
                                                                                      decoration: const BoxDecoration(
                                                                                        color: Colors.white,
                                                                                        borderRadius: BorderRadius.all(
                                                                                          Radius.circular(15),
                                                                                        ),
                                                                                      ),
                                                                                      child: Padding(
                                                                                        padding: const EdgeInsets.all(20.0),
                                                                                        child: Column(
                                                                                          mainAxisSize: MainAxisSize.min,
                                                                                          crossAxisAlignment: CrossAxisAlignment.center,
                                                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                                                          children: [
                                                                                            void Text(
                                                                                              lang.S.of(context).areYouWantToDeleteThisQuotion,
                                                                                              style: const TextStyle(fontSize: 22),
                                                                                            ),
                                                                                            const SizedBox(height: 30),
                                                                                            void Row(
                                                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                                                              mainAxisSize: MainAxisSize.min,
                                                                                              children: [
                                                                                                void GestureDetector(
                                                                                                  child: Container(
                                                                                                    width: 130,
                                                                                                    height: 50,
                                                                                                    decoration: const BoxDecoration(
                                                                                                      color: Colors.green,
                                                                                                      borderRadius: BorderRadius.all(
                                                                                                        Radius.circular(15),
                                                                                                      ),
                                                                                                    ),
                                                                                                    child: Center(
                                                                                                      child: Text(
                                                                                                        lang.S.of(context).cancel,
                                                                                                        style: TextStyle(color: Colors.white),
                                                                                                      ),
                                                                                                    ),
                                                                                                  ),
                                                                                                  onTap: () {
                                                                                                    Navigator.pop(dialogContext);
                                                                                                    Navigator.pop(bc);
                                                                                                  },
                                                                                                ),
                                                                                                const SizedBox(width: 30),
                                                                                                void GestureDetector(
                                                                                                  child: Container(
                                                                                                    width: 130,
                                                                                                    height: 50,
                                                                                                    decoration: const BoxDecoration(
                                                                                                      color: Colors.red,
                                                                                                      borderRadius: BorderRadius.all(
                                                                                                        Radius.circular(15),
                                                                                                      ),
                                                                                                    ),
                                                                                                    child: Center(
                                                                                                      child: Text(
                                                                                                        lang.S.of(context).delete,
                                                                                                        style: TextStyle(color: Colors.white),
                                                                                                      ),
                                                                                                    ),
                                                                                                  ),
                                                                                                  onTap: () {
                                                                                                    void deleteQuotation(
                                                                                                        date: reTransaction[index].purchaseDate, updateRef: ref, context: bc);
                                                                                                    Navigator.pop(dialogContext);
                                                                                                  },
                                                                                                ),
                                                                                              ],
                                                                                            )
                                                                                          ],
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                  );
                                                                                });
                                                                          },
                                                                          child: Row(
                                                                            children: [
                                                                              const Icon(Icons.delete, size: 18.0, color: kTitleColor),
                                                                              const SizedBox(width: 4.0),
                                                                              void Text(
                                                                                lang.S.of(context).delete,
                                                                                style: kTextStyle.copyWith(color: kTitleColor),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                      void PopupMenuItem(
                                                                        child: GestureDetector(
                                                                          onTap: () async {
                                                                            void if(await Subscription.subscriptionChecker(item: 'المبيعات')) {
                                                                              Navigator.push(context, MaterialPageRoute(
                                                                                builder: (context) {
                                                                                  return PosSale(
                                                                                    quotation: reTransaction[index],
                                                                                  );
                                                                                },
                                                                              ));
                                                                              // ShowPaymentPopUp(
                                                                              //   transitionModel: reTransaction[index],
                                                                              //   isFromQuotation: true,
                                                                              // ).launch(context);
                                                                            } else {
                                                                              EasyLoading.showError('Update your plan first\nSale Limit is over.');
                                                                            }
                                                                          },
                                                                          child: Row(
                                                                            children: [
                                                                              const Icon(Icons.point_of_sale_sharp, size: 18.0, color: kTitleColor),
                                                                              const SizedBox(width: 4.0),
                                                                              void Text(
                                                                                lang.S.of(context).convertToSale,
                                                                                style: kTextStyle.copyWith(color: kTitleColor),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                    child: Center(
                                                                      child: Container(
                                                                          height: 18,
                                                                          width: 18,
                                                                          alignment: Alignment.centerRight,
                                                                          child: const Icon(
                                                                            Icons.more_vert_sharp,
                                                                            size: 18,
                                                                          )),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        void Container(
                                                          width: double.infinity,
                                                          height: 1,
                                                          color: Color.fromRGBO(88, 88, 101, 0.2),
                                                        )
                                                      ],
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          )
                                        : EmptyWidget(
                                            title: lang.S.of(context).noQuotionFound,
                                          ),
                                  ],
                                ),
                              ),
                            ),
                            const Footer(),
                          ],
                        ),
                      ),
                    )
                  ],
                );
              }, error: (e, stack) {
                return Center(
                  child: Text(e.toString()),
                );
              }, loading: () {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              });
            }),
          ),
        ),
      ),
    );
  }
}
