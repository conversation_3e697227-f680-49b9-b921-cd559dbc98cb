import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/Repository/sales_return_repo.dart';
import 'package:amrdev_win_pos/model/purchase_transation_model.dart';

import '../Repository/purchase_return_repo.dart';
import '../model/sale_transaction_model.dart';

PurchaseReturnRepo salesReturnRepo = PurchaseReturnRepo();
final purchaseReturnProvider = FutureProvider<List<PurchaseTransactionModel>>((ref) => salesReturnRepo.getAllTransition());
