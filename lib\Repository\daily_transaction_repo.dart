import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/firebase_database_service.dart';
import 'package:amrdev_win_pos/model/daily_transaction_model.dart';

import '../const.dart';

class DailyTransactionRepo {
  Future<List<DailyTransactionModel>> getAllDailyTransition() async {
    List<DailyTransactionModel> dailyTransactionLists = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Daily Transaction').orderByKey().get();
      for (var element in snapshot.children) {
        dailyTransactionLists.add(DailyTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب المعاملات اليومية: $e');
    }
    return dailyTransactionLists;
  }
}
