import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for android - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
  apiKey: "AIzaSyDNJ2l5ssvs4NHl83g5r6WxkburMkBEm_w",
  authDomain: "amrdev-pos-omarhassany.firebaseapp.com",
  databaseURL: "https://amrdev-pos-omarhassany-default-rtdb.firebaseio.com",
  projectId: "amrdev-pos-omarhassany",
  storageBucket: "amrdev-pos-omarhassany.firebasestorage.app",
  messagingSenderId: "138230456121",
  appId: "1:138230456121:web:58a5fd25ee821f880692e8",
  measurementId: "G-LHDYZGB9R7"
  );

  static const FirebaseOptions windows = FirebaseOptions(
  apiKey: "AIzaSyDNJ2l5ssvs4NHl83g5r6WxkburMkBEm_w",
  authDomain: "amrdev-pos-omarhassany.firebaseapp.com",
  databaseURL: "https://amrdev-pos-omarhassany-default-rtdb.firebaseio.com",
  projectId: "amrdev-pos-omarhassany",
  storageBucket: "amrdev-pos-omarhassany.firebasestorage.app",
  messagingSenderId: "138230456121",
  appId: "1:138230456121:web:58a5fd25ee821f880692e8",
  measurementId: "G-LHDYZGB9R7"
  );
}
