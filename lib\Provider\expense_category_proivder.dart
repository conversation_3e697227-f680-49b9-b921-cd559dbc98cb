import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/Repository/expense)category_repo.dart';
import 'package:amrdev_win_pos/model/expense_category_model.dart';
import 'package:amrdev_win_pos/model/income_catehory_model.dart';

ExpenseCategoryRepo _expenseCategoryRepo = ExpenseCategoryRepo(); // متغير غير مستخدم
final _expenseCategoryProvider = FutureProvider.autoDispose<List<ExpenseCategoryModel>>((ref) => expenseCategoryRepo.getAllExpenseCategory()); // متغير غير مستخدم

IncomeCategoryRepo _incomeCategoryRepo = IncomeCategoryRepo(); // متغير غير مستخدم
final _incomeCategoryProvider = FutureProvider.autoDispose<List<IncomeCategoryModel>>((ref) => incomeCategoryRepo.getAllIncomeCategory()); // متغير غير مستخدم
