import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/Repository/expense)category_repo.dart';
import 'package:amrdev_win_pos/model/expense_category_model.dart';
import 'package:amrdev_win_pos/model/income_catehory_model.dart';

ExpenseCategoryRepo expenseCategoryRepo = ExpenseCategoryRepo();
final expenseCategoryProvider = FutureProvider.autoDispose<List<ExpenseCategoryModel>>((ref) => expenseCategoryRepo.getAllExpenseCategory());

IncomeCategoryRepo incomeCategoryRepo = IncomeCategoryRepo();
final incomeCategoryProvider = FutureProvider.autoDispose<List<IncomeCategoryModel>>((ref) => incomeCategoryRepo.getAllIncomeCategory());
