import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

/// خدمة Firebase Database للعمل على جميع المنصات بما في ذلك Windows
/// تستخدم REST API عندما لا تكون firebase_database متاحة
class FirebaseDatabaseService {
  /// الحصول على مرجع قاعدة البيانات
  static DatabaseReference ref([String? path]) {
    // استخدام REST API دائماً للتوافق مع جميع المنصات
    return RestDatabaseReference(path ?? '');
  }

  /// الحصول على مرجع قاعدة البيانات مع مسار
  static DatabaseReference child(String path) {
    return ref(path);
  }
}

/// تنفيذ DatabaseReference باستخدام REST API
class RestDatabaseReference implements DatabaseReference {
  final String _path;
  static const String _databaseUrl = 'https://amrdev-pos-omarhassany-default-rtdb.firebaseio.com';

  RestDatabaseReference(this._path);

  @override
  String get key => _path.split('/').last;

  @override
  DatabaseReference child(String path) {
    String newPath = _path.isEmpty ? path : '$_path/$path';
    return RestDatabaseReference(newPath);
  }

  @override
  DatabaseReference parent() {
    List<String> parts = _path.split('/');
    if (parts.length <= 1) return RestDatabaseReference('');
    parts.removeLast();
    return RestDatabaseReference(parts.join('/'));
  }

  @override
  DatabaseReference root() {
    return RestDatabaseReference('');
  }

  @override
  Future<DataSnapshot> get() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      String url = '$_databaseUrl/$_path.json';
      
      if (user != null) {
        final idToken = await user.getIdToken();
        url += '?auth=$idToken';
      }

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return RestDataSnapshot(_path, data);
      } else {
        throw Exception('فشل في الحصول على البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على البيانات من $_path: $e');
      throw Exception('خطأ في الاتصال بقاعدة البيانات: $e');
    }
  }

  @override
  Future<void> set(Object? value) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      String url = '$_databaseUrl/$_path.json';
      
      if (user != null) {
        final idToken = await user.getIdToken();
        url += '?auth=$idToken';
      }

      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(value),
      );
      
      if (response.statusCode != 200) {
        throw Exception('فشل في حفظ البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات في $_path: $e');
      throw Exception('خطأ في حفظ البيانات: $e');
    }
  }

  @override
  Future<void> update(Map<String, Object?> values) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      String url = '$_databaseUrl/$_path.json';
      
      if (user != null) {
        final idToken = await user.getIdToken();
        url += '?auth=$idToken';
      }

      final response = await http.patch(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(values),
      );
      
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات في $_path: $e');
      throw Exception('خطأ في تحديث البيانات: $e');
    }
  }

  @override
  DatabaseReference push() {
    // إنشاء مفتاح فريد
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = (timestamp % 1000).toString().padLeft(3, '0');
    final pushKey = 'push_${timestamp}_$randomSuffix';
    return child(pushKey);
  }

  @override
  Future<void> remove() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      String url = '$_databaseUrl/$_path.json';
      
      if (user != null) {
        final idToken = await user.getIdToken();
        url += '?auth=$idToken';
      }

      final response = await http.delete(Uri.parse(url));
      
      if (response.statusCode != 200) {
        throw Exception('فشل في حذف البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات من $_path: $e');
      throw Exception('خطأ في حذف البيانات: $e');
    }
  }

  @override
  Query orderByKey() => RestQuery(_path, 'orderBy', '"\$key"');

  @override
  Query orderByValue() => RestQuery(_path, 'orderBy', '"\$value"');

  @override
  Query orderByChild(String path) => RestQuery(_path, 'orderBy', '"$path"');

  // تنفيذ باقي الطرق المطلوبة...
  @override
  String toString() => 'RestDatabaseReference($_path)';
  
  @override
  dynamic noSuchMethod(Invocation invocation) {
    debugPrint('⚠️ طريقة غير مدعومة: ${invocation.memberName}');
    return super.noSuchMethod(invocation);
  }
}

/// تنفيذ DataSnapshot للـ REST API
class RestDataSnapshot implements DataSnapshot {
  final String _path;
  final dynamic _value;
  
  RestDataSnapshot(this._path, this._value);

  @override
  String? get key => _path.split('/').last;

  @override
  dynamic get value => _value;

  @override
  bool get exists => _value != null;

  @override
  Iterable<DataSnapshot> get children {
    if (_value is Map) {
      final map = _value as Map;
      return map.entries.map((entry) => 
        RestDataSnapshot('$_path/${entry.key}', entry.value)
      );
    }
    return [];
  }

  @override
  DataSnapshot child(String path) {
    if (_value is Map) {
      final map = _value as Map;
      return RestDataSnapshot('$_path/$path', map[path]);
    }
    return RestDataSnapshot('$_path/$path', null);
  }

  @override
  bool hasChild(String path) {
    if (_value is Map) {
      return (_value as Map).containsKey(path);
    }
    return false;
  }

  @override
  dynamic noSuchMethod(Invocation invocation) {
    debugPrint('⚠️ طريقة غير مدعومة في DataSnapshot: ${invocation.memberName}');
    return super.noSuchMethod(invocation);
  }
}

/// تنفيذ Query للـ REST API
class RestQuery implements Query {
  final String _path;
  final String _orderBy;
  final String _orderValue;
  static const String _databaseUrl = 'https://amrdev-pos-omarhassany-default-rtdb.firebaseio.com';

  RestQuery(this._path, this._orderBy, this._orderValue);

  @override
  Future<DataSnapshot> get() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      String url = '$_databaseUrl/$_path.json?$_orderBy=$_orderValue';
      
      if (user != null) {
        final idToken = await user.getIdToken();
        url += '&auth=$idToken';
      }

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return RestDataSnapshot(_path, data);
      } else {
        throw Exception('فشل في الحصول على البيانات: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في استعلام البيانات من $_path: $e');
      throw Exception('خطأ في استعلام قاعدة البيانات: $e');
    }
  }

  @override
  dynamic noSuchMethod(Invocation invocation) {
    debugPrint('⚠️ طريقة غير مدعومة في Query: ${invocation.memberName}');
    return super.noSuchMethod(invocation);
  }
}

// تعريف الواجهات المطلوبة
abstract class DatabaseReference {
  String? get key;
  DatabaseReference child(String path);
  DatabaseReference parent();
  DatabaseReference root();
  Future<DataSnapshot> get();
  Future<void> set(Object? value);
  Future<void> update(Map<String, Object?> values);
  DatabaseReference push();
  Future<void> remove();
  Query orderByKey();
  Query orderByValue();
  Query orderByChild(String path);
}

abstract class DataSnapshot {
  String? get key;
  dynamic get value;
  bool get exists;
  Iterable<DataSnapshot> get children;
  DataSnapshot child(String path);
  bool hasChild(String path);
}

abstract class Query {
  Future<DataSnapshot> get();
}


