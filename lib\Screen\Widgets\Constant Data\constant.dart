
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:amrdev_win_pos/model/daily_transaction_model.dart';

import '../../../const.dart';
import '../../../services/firebase_database_service.dart';

String paypalClientId = '';
String paypalClientSecret = '';
const bool sandbox = true;
// const String currency = 'USD';
String countryName = 'Bangladesh';
String selectedCountry = 'English';

// const kMainColor = Color(0xFF3F8CFF);
const kMainColor = Color(0xff8424FF);
const kDarkGreyColor = Color(0xFF2E2E3E);
const kLitGreyColor = Color(0xFFD4D4D8);
const kGreyTextColor = Color(0xFF585865);
const kChartColor = Color(0xff2E2E3E);
const kBorderColorTextField = Color(0xFFE8E7E5);
const kDarkWhite = Color(0xFFF2F6F8);
const kbgColor = Color(0xFFF8F3FF);
const kWhite = Color(0xFFFFFFFF);
const kRedTextColor = Color(0xFFFE2525);
const kBlueTextColor = Color(0xff8424FF);
const kYellowColor = Color(0xFFFF8C00);
const kGreenTextColor = Color(0xff8424FF);
const kTitleColor = Color(0xFF2E2E3E);
const kPremiumPlanColor = Color(0xFF8752EE);
const kPremiumPlanColor2 = Color(0xFFFF5F00);
const lightGreyColor = Color(0xFFF8F3FF);
const dropdownItemColor = Color(0xFFF2F6F8);
final kTextStyle = GoogleFonts.manrope(
  color: Colors.white,
);
final bTextStyle = GoogleFonts.manrope(
  color: Colors.black,
);
const kButtonDecoration = BoxDecoration(
  color: kMainColor,
  borderRadius: BorderRadius.all(
    Radius.circular(40.0),
  ),
);

const kInputDecoration = InputDecoration(
    hintStyle: TextStyle(color: kBorderColorTextField),
    filled: true,
    fillColor: Colors.white70,
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(6.0)),
      borderSide: BorderSide(color: kBorderColorTextField, width: 2),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(6.0)),
      borderSide: BorderSide(color: kBorderColorTextField, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(6.0)),
      borderSide: BorderSide(color: Colors.red, width: 2),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(6.0)),
      borderSide: BorderSide(color: Colors.red, width: 2),
    ));

const bInputDecoration = InputDecoration(
  hintStyle: TextStyle(color: kGreyTextColor),
  filled: true,
  fillColor: Colors.white70,
  enabledBorder: OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(8.0)),
    borderSide: BorderSide(color: kBorderColorTextField, width: 1),
  ),
  focusedBorder: OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(6.0)),
    borderSide: BorderSide(color: kBorderColorTextField, width: 1),
  ),
);

OutlineInputBorder outlineInputBorder() {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(10.0),
    borderSide: const BorderSide(color: kBorderColorTextField),
  );
}

final otpInputDecoration = InputDecoration(
  contentPadding: const EdgeInsets.symmetric(vertical: 5.0),
  border: outlineInputBorder(),
  focusedBorder: outlineInputBorder(),
  enabledBorder: outlineInputBorder(),
);

List<String> businessCategory = ['متجر أزياء', 'متجر إلكترونيات', 'متجر كمبيوتر', 'متجر خضروات', 'متجر حلويات', 'متجر لحوم'];
List<String> language = ['العربية'];

List<String> productCategory = ['أزياء', 'إلكترونيات', 'كمبيوتر', 'أجهزة', 'ساعات', 'ملابس'];

List<String> userRole = [
  'مدير عام',
  'مدير',
  'مستخدم',
];

List<String> paymentType = [
  'شيك',
  'إيداع',
  'نقدي',
  'تحويل',
  'فودافون كاش',
  'إنستاباي',
];
List<String> posStats = [
  'يومي',
  'شهري',
  'سنوي',
];
List<String> saleStats = [
  'أسبوعي',
  'شهري',
  'سنوي',
];

void updateInvoice({required String typeOfInvoice, required int invoice}) async {
  ///_______invoice_Update_____________________________________________
  final personalInformationRef = FirebaseDatabaseService.ref(await getUserID()).child('Personal Information');

  await personalInformationRef.update({typeOfInvoice: invoice + 1});
}

Future<void> postDailyTransaction({required DailyTransactionModel dailyTransactionModel}) async {
  final personalInformationRef = FirebaseDatabaseService.ref(await getUserID()).child('Personal Information');
  double remainingBalance = 0;

  await personalInformationRef.orderByKey().get().then((value) {
    var data = jsonDecode(jsonEncode(value.value));
    remainingBalance = data['remainingShopBalance'];
  });

  if (dailyTransactionModel.type == 'بيع' ||
      dailyTransactionModel.type == 'Due Collection' ||
      dailyTransactionModel.type == 'Income' ||
      dailyTransactionModel.type == 'Purchase Return') {
    remainingBalance += dailyTransactionModel.paymentIn;
  } else {
    remainingBalance -= dailyTransactionModel.paymentOut;
  }

  dailyTransactionModel.remainingBalance = remainingBalance;

  ///________post_remaining Balance_on_personal_information___________________________________________________
  await personalInformationRef.update({'remainingShopBalance': remainingBalance});

  ///_________dailyTransaction_Posting________________________________________________________________________
  final dailyTransactionRef = FirebaseDatabaseService.ref("${await getUserID()}/Daily Transaction");
  await dailyTransactionRef.push().set(dailyTransactionModel.toJson());
}
