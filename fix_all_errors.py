#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت شامل لحل جميع الأخطاء المتكررة في مشروع Flutter POS
يحل المشاكل التالية:
1. إضافة dart:convert للملفات التي تحتاجه
2. إزالة الاستيرادات غير المستخدمة
3. استبدال image_picker_web بـ image_picker
4. استبدال FirebaseDatabase بـ FirebaseDatabaseService
5. إصلاح مشاكل العملة واللغة

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
import re
import sys
from pathlib import Path
import argparse

class FlutterErrorFixer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.changes_made = 0
        self.files_processed = 0
        self.detailed_changes = []

    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")
        
        for file_path in self.project_path.rglob("*.dart"):
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)
            
        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")

    def fix_imports(self, content, file_path):
        """إصلاح الاستيرادات"""
        changes = 0
        lines = content.split('\n')
        new_lines = []
        
        # تتبع الاستيرادات الموجودة
        has_dart_convert = False
        has_firebase_database = False
        has_image_picker_web = False
        has_firebase_database_service = False
        
        for line in lines:
            # فحص الاستيرادات الموجودة
            if "import 'dart:convert';" in line:
                has_dart_convert = True
            elif "import 'package:firebase_database/firebase_database.dart';" in line:
                has_firebase_database = True
                # استبدال firebase_database بـ firebase_database_service
                relative_path = self.get_relative_path_to_services(file_path)
                new_line = f"import '{relative_path}services/firebase_database_service.dart';"
                new_lines.append(new_line)
                changes += 1
                continue
            elif "import 'package:image_picker_web/image_picker_web.dart';" in line:
                has_image_picker_web = True
                # استبدال image_picker_web بـ image_picker
                new_line = "import 'package:image_picker/image_picker.dart';"
                new_lines.append(new_line)
                changes += 1
                continue
            elif "firebase_database_service.dart" in line:
                has_firebase_database_service = True
            
            new_lines.append(line)
        
        # إضافة dart:convert إذا كان مطلوباً
        if not has_dart_convert and self.needs_dart_convert(content):
            # البحث عن مكان إدراج dart:convert
            insert_index = 0
            for i, line in enumerate(new_lines):
                if line.startswith("import 'dart:"):
                    insert_index = i + 1
                elif line.startswith("import 'package:") or line.startswith("import '"):
                    if insert_index == 0:
                        insert_index = i
                    break
            
            new_lines.insert(insert_index, "import 'dart:convert';")
            changes += 1
        
        return '\n'.join(new_lines), changes

    def needs_dart_convert(self, content):
        """فحص ما إذا كان الملف يحتاج dart:convert"""
        return 'jsonDecode' in content or 'jsonEncode' in content

    def get_relative_path_to_services(self, file_path):
        """حساب المسار النسبي لمجلد services"""
        # حساب عدد المستويات من الملف الحالي إلى lib
        relative_to_lib = file_path.relative_to(self.project_path / 'lib')
        depth = len(relative_to_lib.parts) - 1
        
        if depth == 0:
            return ""
        else:
            return "../" * depth

    def fix_firebase_database_usage(self, content):
        """إصلاح استخدام FirebaseDatabase"""
        changes = 0
        
        # استبدال FirebaseDatabase.instance.ref() بـ FirebaseDatabaseService.ref()
        patterns = [
            (r'FirebaseDatabase\.instance\.ref\(\)', 'FirebaseDatabaseService.ref()'),
            (r'FirebaseDatabase\.instance\.ref\(([^)]+)\)', r'FirebaseDatabaseService.ref(\1)'),
            (r'final DatabaseReference (\w+) = FirebaseDatabase\.instance\.ref\(\)\.child\(([^)]+)\)\.child\(([^)]+)\);',
             r'final \1 = FirebaseDatabaseService.ref(\2).child(\3);'),
            (r'final DatabaseReference (\w+) = FirebaseDatabase\.instance\.ref\(([^)]+)\)\.child\(([^)]+)\);',
             r'final \1 = FirebaseDatabaseService.ref(\2).child(\3);'),
            (r'DatabaseReference (\w+) = FirebaseDatabase\.instance\.ref\(([^)]+)\);',
             r'final \1 = FirebaseDatabaseService.ref(\2);'),
        ]
        
        for pattern, replacement in patterns:
            new_content, count = re.subn(pattern, replacement, content)
            if count > 0:
                content = new_content
                changes += count
        
        return content, changes

    def fix_image_picker_usage(self, content):
        """إصلاح استخدام ImagePickerWeb"""
        changes = 0
        
        # استبدال ImagePickerWeb.getImageAsBytes() بـ ImagePicker
        old_pattern = r'if \(kIsWeb\) \{\s*try \{\s*Uint8List\? bytesFromPicker = await ImagePickerWeb\.getImageAsBytes\(\);'
        new_pattern = '''try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        Uint8List bytesFromPicker = await pickedFile.readAsBytes();'''
        
        if old_pattern in content:
            content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE | re.DOTALL)
            changes += 1
        
        return content, changes

    def fix_currency_and_language(self, content):
        """إصلاح العملة واللغة الافتراضية"""
        changes = 0
        
        # استبدال العملة من $ إلى ج.م
        if "currency: '\\$'" in content:
            content = content.replace("currency: '\\$'", "currency: 'ج.م'")
            changes += 1
        
        # استبدال اللغة الافتراضية
        if "currentLocale: 'en'" in content:
            content = content.replace("currentLocale: 'en'", "currentLocale: 'ar'")
            changes += 1
        
        if "language: ''" in content:
            content = content.replace("language: ''", "language: 'العربية'")
            changes += 1
        
        return content, changes

    def process_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_changes = 0
            
            # إصلاح الاستيرادات
            content, changes = self.fix_imports(content, file_path)
            file_changes += changes
            
            # إصلاح استخدام FirebaseDatabase
            content, changes = self.fix_firebase_database_usage(content)
            file_changes += changes
            
            # إصلاح استخدام ImagePicker
            content, changes = self.fix_image_picker_usage(content)
            file_changes += changes
            
            # إصلاح العملة واللغة
            content, changes = self.fix_currency_and_language(content)
            file_changes += changes
            
            # كتابة الملف إذا تم إجراء تغييرات
            if file_changes > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.detailed_changes.append({
                    'file': str(file_path),
                    'changes': file_changes
                })
                
                print(f"✅ تم إصلاح {file_changes} مشكلة في {file_path.name}")
            
            self.changes_made += file_changes
            self.files_processed += 1
            
        except Exception as e:
            print(f"❌ خطأ في معالجة {file_path}: {e}")

    def run(self):
        """تشغيل المعالج"""
        print("🚀 بدء إصلاح الأخطاء المتكررة في مشروع Flutter...")
        
        self.find_dart_files()
        
        for file_path in self.dart_files:
            self.process_file(file_path)
        
        self.print_summary()

    def print_summary(self):
        """طباعة ملخص النتائج"""
        print("\n" + "="*60)
        print("📊 ملخص الإصلاحات")
        print("="*60)
        print(f"📁 الملفات المعالجة: {self.files_processed}")
        print(f"🔧 إجمالي الإصلاحات: {self.changes_made}")
        
        if self.detailed_changes:
            print("\n📋 تفاصيل الإصلاحات:")
            for change in self.detailed_changes:
                print(f"  • {change['file']}: {change['changes']} إصلاح")
        
        print("\n✅ تم الانتهاء من إصلاح الأخطاء!")
        print("💡 يُنصح بتشغيل 'flutter analyze' للتأكد من عدم وجود أخطاء أخرى.")

def main():
    parser = argparse.ArgumentParser(description='إصلاح الأخطاء المتكررة في مشروع Flutter')
    parser.add_argument('--path', default='.', help='مسار المشروع (افتراضي: المجلد الحالي)')
    
    args = parser.parse_args()
    
    fixer = FlutterErrorFixer(args.path)
    fixer.run()

if __name__ == "__main__":
    main()
