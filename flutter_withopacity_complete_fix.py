import os

def restore_backups(project_dir):
    restored_files = 0
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            if file.endswith('.dart.backup'):
                backup_path = os.path.join(root, file)
                original_filename = file.replace('.dart.backup', '.dart')
                original_path = os.path.join(root, original_filename)

                # حذف الملف المعدل
                if os.path.exists(original_path):
                    os.remove(original_path)

                # إعادة التسمية من .backup إلى الملف الأصلي
                os.rename(backup_path, original_path)
                print(f"✅ Restored: {original_filename}")
                restored_files += 1

    if restored_files == 0:
        print("🚫 No .dart.backup files found.")
    else:
        print(f"\n✅ Restoration complete. {restored_files} files restored.")

# ضع هنا مسار مشروعك الكامل
project_directory = r"D:\work\pos-win"
restore_backups(project_directory)
