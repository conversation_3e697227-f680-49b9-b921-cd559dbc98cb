import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

/// أداة تشخيص مشاكل تسجيل الدخول
class LoginDebugger {
  
  /// تشخيص شامل لمشاكل تسجيل الدخول
  static Future<void> diagnoseLoginIssues(String email, String password) async {
    debugPrint('🔍 بدء تشخيص مشاكل تسجيل الدخول...');
    debugPrint('📧 الإيميل: $email');
    debugPrint('🔒 كلمة المرور: ${password.replaceAll(RegExp(r'.'), '*')}');
    
    // 1. فحص اتصال Firebase Auth
    await _testFirebaseAuth(email, password);
    
    // 2. فحص اتصال Firebase Database
    await _testFirebaseDatabase();
    
    // 3. فحص بيانات المستخدمين الفرعيين
    await _testSubUsers(email);
    
    debugPrint('✅ انتهاء التشخيص');
  }
  
  /// فحص Firebase Authentication
  static Future<void> _testFirebaseAuth(String email, String password) async {
    debugPrint('\n🔐 فحص Firebase Authentication...');
    
    try {
      // محاولة تسجيل الدخول
      final userCredential = await FirebaseAuth.instance
          .signInWithEmailAndPassword(email: email, password: password);
      
      if (userCredential.user != null) {
        debugPrint('✅ تم تسجيل الدخول بنجاح في Firebase Auth');
        debugPrint('📧 الإيميل: ${userCredential.user!.email}');
        debugPrint('🆔 UID: ${userCredential.user!.uid}');
        debugPrint('✅ تم التحقق: ${userCredential.user!.emailVerified}');
        
        // اختبار الحصول على ID Token
        try {
          final idToken = await userCredential.user!.getIdToken();
          debugPrint('🔑 تم الحصول على ID Token بنجاح');
        } catch (e) {
          debugPrint('❌ فشل في الحصول على ID Token: $e');
        }
        
      } else {
        debugPrint('❌ فشل في تسجيل الدخول - لا يوجد مستخدم');
      }
      
    } on FirebaseAuthException catch (e) {
      debugPrint('❌ خطأ في Firebase Auth: ${e.code}');
      debugPrint('📝 الرسالة: ${e.message}');
      
      switch (e.code) {
        case 'user-not-found':
          debugPrint('💡 المستخدم غير موجود في Firebase Auth');
          debugPrint('🔧 الحل: إنشاء حساب جديد أو التأكد من الإيميل');
          break;
        case 'wrong-password':
          debugPrint('💡 كلمة المرور خاطئة');
          debugPrint('🔧 الحل: التأكد من كلمة المرور أو إعادة تعيينها');
          break;
        case 'invalid-email':
          debugPrint('💡 تنسيق الإيميل خاطئ');
          debugPrint('🔧 الحل: التأكد من تنسيق الإيميل');
          break;
        case 'user-disabled':
          debugPrint('💡 تم تعطيل هذا المستخدم');
          debugPrint('🔧 الحل: تفعيل المستخدم من Firebase Console');
          break;
        case 'too-many-requests':
          debugPrint('💡 محاولات كثيرة جداً');
          debugPrint('🔧 الحل: الانتظار قليلاً ثم المحاولة مرة أخرى');
          break;
        default:
          debugPrint('💡 خطأ غير معروف: ${e.code}');
      }
    } catch (e) {
      debugPrint('❌ خطأ عام في Firebase Auth: $e');
    }
  }
  
  /// فحص Firebase Database
  static Future<void> _testFirebaseDatabase() async {
    debugPrint('\n🗄️ فحص Firebase Database...');
    
    try {
      // اختبار كتابة بيانات
      final testRef = FirebaseDatabaseService.ref('debug_test');
      final testData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'message': 'اختبار الاتصال',
        'platform': 'Windows'
      };
      
      await testRef.set(testData);
      debugPrint('✅ تم كتابة البيانات بنجاح');
      
      // اختبار قراءة البيانات
      final snapshot = await testRef.get();
      if (snapshot.exists) {
        debugPrint('✅ تم قراءة البيانات بنجاح');
        debugPrint('📊 البيانات: ${jsonEncode(snapshot.value)}');
      } else {
        debugPrint('⚠️ لم يتم العثور على البيانات');
      }
      
      // حذف البيانات التجريبية
      await testRef.remove();
      debugPrint('✅ تم حذف البيانات التجريبية');
      
    } catch (e) {
      debugPrint('❌ خطأ في Firebase Database: $e');
      debugPrint('🔧 الحل المحتمل: التأكد من قواعد الأمان في Firebase Database');
    }
  }
  
  /// فحص بيانات المستخدمين الفرعيين
  static Future<void> _testSubUsers(String email) async {
    debugPrint('\n👥 فحص بيانات المستخدمين الفرعيين...');
    
    try {
      final snapshot = await FirebaseDatabaseService.ref('Admin Panel')
          .child('User Role')
          .get();
      
      if (!snapshot.exists || snapshot.value == null) {
        debugPrint('ℹ️ لا توجد بيانات مستخدمين فرعيين');
        debugPrint('💡 هذا طبيعي إذا كان المستخدم رئيسي');
        return;
      }
      
      debugPrint('📊 عدد المستخدمين الفرعيين: ${snapshot.children.length}');
      
      bool foundUser = false;
      for (var element in snapshot.children) {
        try {
          if (element.value == null) continue;
          
          final data = jsonDecode(jsonEncode(element.value));
          final userEmail = data['email'] ?? '';
          
          debugPrint('🔍 مستخدم فرعي: $userEmail');
          
          if (userEmail == email) {
            debugPrint('✅ تم العثور على المستخدم الفرعي: $email');
            debugPrint('📋 البيانات: ${jsonEncode(data)}');
            foundUser = true;
            break;
          }
        } catch (e) {
          debugPrint('❌ خطأ في معالجة بيانات مستخدم فرعي: $e');
        }
      }
      
      if (!foundUser) {
        debugPrint('ℹ️ لم يتم العثور على المستخدم كمستخدم فرعي');
        debugPrint('💡 سيتم التعامل معه كمستخدم رئيسي');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص المستخدمين الفرعيين: $e');
    }
  }
  
  /// فحص إعدادات Firebase
  static void checkFirebaseConfig() {
    debugPrint('\n⚙️ فحص إعدادات Firebase...');
    
    try {
      final auth = FirebaseAuth.instance;
      debugPrint('✅ Firebase Auth متاح');
      debugPrint('🔧 التطبيق: ${auth.app.name}');
      
      final currentUser = auth.currentUser;
      if (currentUser != null) {
        debugPrint('👤 مستخدم مسجل دخول: ${currentUser.email}');
      } else {
        debugPrint('👤 لا يوجد مستخدم مسجل دخول');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في إعدادات Firebase: $e');
    }
  }
}
