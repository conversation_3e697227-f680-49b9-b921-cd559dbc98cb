#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإزالة الاستيرادات غير المستخدمة

المؤلف: مساعد الذكي
التاريخ: 2025-07-16
"""

import os
from pathlib import Path

def remove_unused_foundation_import(file_path):
    """إزالة استيراد flutter/foundation.dart غير المستخدم"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص إذا كان الاستيراد مستخدم فعلاً
        if "import 'package:flutter/foundation.dart';" in content:
            # فحص إذا كان kDebugMode أو debugPrint مستخدم
            if 'kDebugMode' not in content and 'debugPrint' not in content and 'kIsWeb' not in content:
                # إزالة الاستيراد
                content = content.replace("import 'package:flutter/foundation.dart';\n", "")
                content = content.replace("import 'package:flutter/foundation.dart';", "")
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إزالة الاستيراد غير المستخدم من {file_path.name}")
                return True
    
    except Exception as e:
        print(f"❌ خطأ في معالجة {file_path}: {e}")
    
    return False

def main():
    files_to_fix = [
        "lib/Screen/Authentication/profile_setup.dart",
        "lib/Screen/Customer List/add_customer.dart", 
        "lib/Screen/Customer List/edit_customer.dart",
        "lib/Screen/Product/edit_product.dart",
        "lib/Screen/Purchase/purchase.dart",
        "lib/Screen/Settings/settings_screen.dart",
        "lib/Screen/Widgets/Pop UP/Pos Sale/add_item_popup.dart"
    ]
    
    print("🧹 إزالة الاستيرادات غير المستخدمة...")
    
    fixed_count = 0
    for file_path_str in files_to_fix:
        file_path = Path(file_path_str)
        if remove_unused_foundation_import(file_path):
            fixed_count += 1
    
    print(f"\n✅ تم إصلاح {fixed_count} ملف")

if __name__ == "__main__":
    main()
